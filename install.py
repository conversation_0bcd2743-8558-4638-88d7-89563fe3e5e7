#!/usr/bin/env python3
"""
Installation script for the Real-Time Chat Application.
This script sets up the development environment automatically.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

class ChatAppInstaller:
    def __init__(self):
        self.root_dir = Path(__file__).parent
        self.backend_dir = self.root_dir / "backend"
        self.client_dir = self.root_dir / "client"
        self.python_cmd = sys.executable
    
    def check_python_version(self):
        """Check if Python version is compatible."""
        print("🐍 Checking Python version...")
        
        if sys.version_info < (3, 8):
            print(f"❌ Python 3.8+ required, found {sys.version}")
            print("💡 Please upgrade Python: https://python.org/downloads/")
            return False
        
        print(f"✅ Python {sys.version.split()[0]} is compatible")
        return True
    
    def check_pip(self):
        """Check if pip is available."""
        print("📦 Checking pip...")
        
        try:
            subprocess.run([self.python_cmd, "-m", "pip", "--version"], 
                         check=True, capture_output=True)
            print("✅ pip is available")
            return True
        except subprocess.CalledProcessError:
            print("❌ pip is not available")
            print("💡 Install pip: https://pip.pypa.io/en/stable/installation/")
            return False
    
    def install_backend_dependencies(self):
        """Install backend dependencies."""
        print("🔧 Installing backend dependencies...")
        
        try:
            # Change to backend directory
            original_dir = os.getcwd()
            os.chdir(self.backend_dir)
            
            # Install requirements
            subprocess.run([
                self.python_cmd, "-m", "pip", "install", "-r", "requirements.txt"
            ], check=True)
            
            print("✅ Backend dependencies installed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install backend dependencies: {e}")
            return False
        except FileNotFoundError:
            print("❌ Backend requirements.txt not found")
            return False
        finally:
            os.chdir(original_dir)
    
    def install_client_dependencies(self):
        """Install client dependencies."""
        print("🖥️  Installing client dependencies...")
        
        try:
            # Change to client directory
            original_dir = os.getcwd()
            os.chdir(self.client_dir)
            
            # Install requirements
            subprocess.run([
                self.python_cmd, "-m", "pip", "install", "-r", "requirements.txt"
            ], check=True)
            
            print("✅ Client dependencies installed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install client dependencies: {e}")
            return False
        except FileNotFoundError:
            print("❌ Client requirements.txt not found")
            return False
        finally:
            os.chdir(original_dir)
    
    def check_tkinter(self):
        """Check if tkinter is available."""
        print("🖼️  Checking tkinter...")
        
        try:
            import tkinter
            print("✅ tkinter is available")
            return True
        except ImportError:
            print("❌ tkinter is not available")
            
            system = platform.system().lower()
            if system == "linux":
                print("💡 Install tkinter on Ubuntu/Debian: sudo apt-get install python3-tk")
                print("💡 Install tkinter on CentOS/RHEL: sudo yum install tkinter")
            elif system == "darwin":
                print("💡 tkinter should be included with Python on macOS")
                print("💡 If missing, reinstall Python from python.org")
            elif system == "windows":
                print("💡 tkinter should be included with Python on Windows")
                print("💡 If missing, reinstall Python with tkinter option checked")
            
            return False
    
    def create_environment_files(self):
        """Create environment configuration files."""
        print("⚙️  Creating environment files...")
        
        # Backend .env
        backend_env = self.backend_dir / ".env"
        if not backend_env.exists():
            with open(backend_env, "w") as f:
                f.write("# Development Environment Configuration\n")
                f.write("SECRET_KEY=dev-secret-key-change-in-production\n")
                f.write("DATABASE_URL=sqlite+aiosqlite:///./chat_app.db\n")
                f.write("DEBUG=True\n")
                f.write("CORS_ORIGINS=*\n")
            print("✅ Created backend/.env")
        else:
            print("ℹ️  backend/.env already exists")
        
        # Client .env
        client_env = self.client_dir / ".env"
        if not client_env.exists():
            with open(client_env, "w") as f:
                f.write("# Client Configuration\n")
                f.write("API_BASE_URL=http://localhost:8000\n")
            print("✅ Created client/.env")
        else:
            print("ℹ️  client/.env already exists")
    
    def test_installation(self):
        """Test if installation was successful."""
        print("🧪 Testing installation...")
        
        # Test backend imports
        try:
            original_dir = os.getcwd()
            os.chdir(self.backend_dir)
            
            # Test critical imports
            subprocess.run([
                self.python_cmd, "-c", 
                "import fastapi, uvicorn, sqlalchemy, websockets; print('Backend imports OK')"
            ], check=True, capture_output=True)
            
            print("✅ Backend imports successful")
            
        except subprocess.CalledProcessError:
            print("❌ Backend import test failed")
            return False
        finally:
            os.chdir(original_dir)
        
        # Test client imports
        try:
            original_dir = os.getcwd()
            os.chdir(self.client_dir)
            
            # Test critical imports
            subprocess.run([
                self.python_cmd, "-c", 
                "import tkinter, requests, websockets; print('Client imports OK')"
            ], check=True, capture_output=True)
            
            print("✅ Client imports successful")
            
        except subprocess.CalledProcessError:
            print("❌ Client import test failed")
            return False
        finally:
            os.chdir(original_dir)
        
        return True
    
    def print_next_steps(self):
        """Print next steps for the user."""
        print("\n🎉 Installation completed successfully!")
        print("=" * 50)
        print("📋 Next steps:")
        print()
        print("1. Start the development environment:")
        print("   python start_dev.py")
        print()
        print("2. Or start services manually:")
        print("   Backend: cd backend && uvicorn app.main:app --reload")
        print("   Client:  cd client && python main.py")
        print()
        print("3. Test the API:")
        print("   python backend/test_api.py")
        print()
        print("4. Access the application:")
        print("   • API: http://localhost:8000")
        print("   • Docs: http://localhost:8000/docs")
        print("   • Client: GUI application window")
        print()
        print("📚 Documentation:")
        print("   • README.md - Project overview")
        print("   • DEPLOYMENT.md - Deployment guide")
        print()
        print("🐛 Troubleshooting:")
        print("   • Check logs for any error messages")
        print("   • Ensure no other services are using port 8000")
        print("   • Verify all dependencies are installed correctly")
        print()
        print("Happy chatting! 💬")
    
    def install(self):
        """Run the complete installation process."""
        print("🚀 Real-Time Chat Application Installer")
        print("=" * 50)
        print()
        
        # Check prerequisites
        if not self.check_python_version():
            return False
        
        if not self.check_pip():
            return False
        
        if not self.check_tkinter():
            return False
        
        print()
        
        # Install dependencies
        if not self.install_backend_dependencies():
            return False
        
        if not self.install_client_dependencies():
            return False
        
        print()
        
        # Setup environment
        self.create_environment_files()
        
        print()
        
        # Test installation
        if not self.test_installation():
            print("⚠️  Installation completed with warnings")
            print("💡 Some components may not work correctly")
            return False
        
        # Print next steps
        self.print_next_steps()
        
        return True

def main():
    """Main entry point."""
    installer = ChatAppInstaller()
    
    try:
        success = installer.install()
        if not success:
            print("\n💥 Installation failed!")
            print("💡 Please check the error messages above and try again")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error during installation: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
