import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import os

from helpers import APIClient, TokenManager, format_timestamp, truncate_text
from websocket_client import WebSocketClient

class ChatWindow:
    """Main chat window interface."""
    
    def __init__(self, parent: ttk.Frame, user_info: Dict[str, Any], 
                 api_client: APIClient, token_manager: TokenManager, 
                 on_logout: Callable):
        self.parent = parent
        self.user_info = user_info
        self.api_client = api_client
        self.token_manager = token_manager
        self.on_logout = on_logout
        
        # State
        self.chat_rooms: List[Dict[str, Any]] = []
        self.current_room: Optional[Dict[str, Any]] = None
        self.messages: Dict[int, List[Dict[str, Any]]] = {}  # room_id -> messages
        self.typing_users: Dict[int, set] = {}  # room_id -> set of usernames
        self.online_users: set = set()
        
        # WebSocket client
        self.ws_client: Optional[WebSocketClient] = None
        
        # Typing timer
        self.typing_timer: Optional[str] = None
        
        self.setup_ui()
        self.load_chat_rooms()
        self.setup_websocket()
    
    def setup_ui(self):
        """Set up the chat window UI."""
        # Clear parent frame
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # Main container
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(0, weight=1)
        
        self.setup_sidebar()
        self.setup_chat_area()
        self.setup_status_bar()
    
    def setup_sidebar(self):
        """Set up the sidebar with chat rooms and users."""
        # Sidebar frame
        self.sidebar_frame = ttk.Frame(self.main_frame, width=250)
        self.sidebar_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        self.sidebar_frame.grid_propagate(False)
        
        # User info section
        user_frame = ttk.LabelFrame(self.sidebar_frame, text="User", padding="5")
        user_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        user_label = ttk.Label(user_frame, text=f"Welcome, {self.user_info['username']}!")
        user_label.grid(row=0, column=0, sticky=tk.W)
        
        logout_button = ttk.Button(user_frame, text="Logout", command=self.handle_logout)
        logout_button.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        # Chat rooms section
        rooms_frame = ttk.LabelFrame(self.sidebar_frame, text="Chat Rooms", padding="5")
        rooms_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        rooms_frame.rowconfigure(1, weight=1)
        
        # New room button
        new_room_button = ttk.Button(rooms_frame, text="+ New Room", command=self.show_new_room_dialog)
        new_room_button.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Rooms listbox
        self.rooms_listbox = tk.Listbox(rooms_frame, height=8)
        self.rooms_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.rooms_listbox.bind('<<ListboxSelect>>', self.on_room_select)
        
        # Online users section
        users_frame = ttk.LabelFrame(self.sidebar_frame, text="Online Users", padding="5")
        users_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        users_frame.rowconfigure(0, weight=1)
        
        self.users_listbox = tk.Listbox(users_frame, height=6)
        self.users_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.users_listbox.bind('<Double-Button-1>', self.start_private_chat)
    
    def setup_chat_area(self):
        """Set up the main chat area."""
        # Chat area frame
        self.chat_frame = ttk.Frame(self.main_frame)
        self.chat_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.chat_frame.columnconfigure(0, weight=1)
        self.chat_frame.rowconfigure(1, weight=1)
        
        # Chat header
        self.chat_header = ttk.LabelFrame(self.chat_frame, text="Select a chat room", padding="5")
        self.chat_header.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.room_info_label = ttk.Label(self.chat_header, text="No room selected")
        self.room_info_label.grid(row=0, column=0, sticky=tk.W)
        
        # Messages area
        messages_frame = ttk.Frame(self.chat_frame)
        messages_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 5))
        messages_frame.columnconfigure(0, weight=1)
        messages_frame.rowconfigure(0, weight=1)
        
        # Messages text widget with scrollbar
        self.messages_text = scrolledtext.ScrolledText(
            messages_frame, 
            state=tk.DISABLED, 
            wrap=tk.WORD,
            height=20
        )
        self.messages_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Typing indicator
        self.typing_label = ttk.Label(self.chat_frame, text="", foreground="gray")
        self.typing_label.grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        
        # Message input area
        input_frame = ttk.Frame(self.chat_frame)
        input_frame.grid(row=3, column=0, sticky=(tk.W, tk.E))
        input_frame.columnconfigure(0, weight=1)
        
        # Message entry
        self.message_var = tk.StringVar()
        self.message_entry = ttk.Entry(input_frame, textvariable=self.message_var)
        self.message_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        self.message_entry.bind('<Return>', self.send_message)
        self.message_entry.bind('<KeyPress>', self.on_typing)
        self.message_entry.bind('<KeyRelease>', self.on_typing_stop)
        
        # Send button
        self.send_button = ttk.Button(input_frame, text="Send", command=self.send_message)
        self.send_button.grid(row=0, column=1)
        
        # File button
        self.file_button = ttk.Button(input_frame, text="📎", command=self.send_file)
        self.file_button.grid(row=0, column=2, padx=(5, 0))
        
        # Initially disable input
        self.set_input_enabled(False)
    
    def setup_status_bar(self):
        """Set up the status bar."""
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        self.connection_status_label = ttk.Label(self.status_frame, text="Disconnected", foreground="red")
        self.connection_status_label.grid(row=0, column=0, sticky=tk.W)
        
        # Add a separator
        ttk.Separator(self.status_frame, orient=tk.VERTICAL).grid(row=0, column=1, sticky=(tk.N, tk.S), padx=10)
        
        self.status_label = ttk.Label(self.status_frame, text="Ready")
        self.status_label.grid(row=0, column=2, sticky=tk.W)
    
    def setup_websocket(self):
        """Set up WebSocket connection."""
        token = self.token_manager.get_access_token()
        if token:
            self.ws_client = WebSocketClient(token, self.handle_websocket_message)
            self.ws_client.start()
            
            # Update connection status periodically
            self.update_connection_status()
    
    def update_connection_status(self):
        """Update connection status display."""
        if self.ws_client:
            status = self.ws_client.connection_status
            color = "green" if status == "Connected" else "orange" if "Connecting" in status else "red"
            self.connection_status_label.config(text=status, foreground=color)
        
        # Schedule next update
        self.parent.after(2000, self.update_connection_status)
    
    def load_chat_rooms(self):
        """Load chat rooms from API."""
        def load_rooms_thread():
            try:
                rooms = self.api_client.get_chat_rooms()
                self.parent.after(0, lambda: self.update_rooms_list(rooms))
            except Exception as e:
                self.parent.after(0, lambda: self.show_status(f"Error loading rooms: {e}", "red"))
        
        threading.Thread(target=load_rooms_thread, daemon=True).start()
    
    def update_rooms_list(self, rooms: List[Dict[str, Any]]):
        """Update the rooms list in the UI."""
        self.chat_rooms = rooms
        
        # Clear and populate listbox
        self.rooms_listbox.delete(0, tk.END)
        for room in rooms:
            room_name = room['name']
            if room.get('member_count', 0) > 0:
                room_name += f" ({room['member_count']})"
            self.rooms_listbox.insert(tk.END, room_name)
    
    def on_room_select(self, event):
        """Handle room selection."""
        selection = self.rooms_listbox.curselection()
        if selection:
            room_index = selection[0]
            if room_index < len(self.chat_rooms):
                self.select_room(self.chat_rooms[room_index])
    
    def select_room(self, room: Dict[str, Any]):
        """Select and load a chat room."""
        self.current_room = room
        
        # Update header
        room_type = "Group" if room.get('is_group', False) else "Private"
        self.chat_header.config(text=f"{room_type} Chat: {room['name']}")
        self.room_info_label.config(text=f"Room ID: {room['id']} | Members: {room.get('member_count', 0)}")
        
        # Enable input
        self.set_input_enabled(True)
        
        # Load messages
        self.load_messages(room['id'])
        
        # Join room via WebSocket
        if self.ws_client:
            self.ws_client.join_room(room['id'])
        
        self.show_status(f"Joined room: {room['name']}")
    
    def set_input_enabled(self, enabled: bool):
        """Enable or disable message input."""
        state = tk.NORMAL if enabled else tk.DISABLED
        self.message_entry.config(state=state)
        self.send_button.config(state=state)
        self.file_button.config(state=state)
    
    def load_messages(self, room_id: int):
        """Load messages for a room."""
        def load_messages_thread():
            try:
                messages = self.api_client.get_messages(room_id)
                self.parent.after(0, lambda: self.display_messages(room_id, messages))
            except Exception as e:
                self.parent.after(0, lambda: self.show_status(f"Error loading messages: {e}", "red"))
        
        threading.Thread(target=load_messages_thread, daemon=True).start()
    
    def display_messages(self, room_id: int, messages: List[Dict[str, Any]]):
        """Display messages in the chat area."""
        if not self.current_room or self.current_room['id'] != room_id:
            return
        
        # Store messages
        self.messages[room_id] = messages
        
        # Clear and populate messages
        self.messages_text.config(state=tk.NORMAL)
        self.messages_text.delete(1.0, tk.END)
        
        for message in messages:
            self.add_message_to_display(message)
        
        self.messages_text.config(state=tk.DISABLED)
        self.messages_text.see(tk.END)
    
    def add_message_to_display(self, message: Dict[str, Any]):
        """Add a single message to the display."""
        timestamp = format_timestamp(message['created_at'])
        sender = message.get('sender_username', 'Unknown')
        content = message['content']
        
        # Format message
        if message.get('is_edited', False):
            content += " (edited)"
        
        message_text = f"[{timestamp}] {sender}: {content}\n"
        
        # Add to text widget
        self.messages_text.config(state=tk.NORMAL)
        self.messages_text.insert(tk.END, message_text)
        self.messages_text.config(state=tk.DISABLED)
        self.messages_text.see(tk.END)

    def send_message(self, event=None):
        """Send a message."""
        if not self.current_room:
            return

        content = self.message_var.get().strip()
        if not content:
            return

        # Clear input
        self.message_var.set("")

        # Stop typing indicator
        if self.ws_client:
            self.ws_client.stop_typing(self.current_room['id'])

        # Send message via API
        def send_message_thread():
            try:
                result = self.api_client.send_message(self.current_room['id'], content)
                if not result:
                    self.parent.after(0, lambda: self.show_status("Failed to send message", "red"))
            except Exception as e:
                self.parent.after(0, lambda: self.show_status(f"Error sending message: {e}", "red"))

        threading.Thread(target=send_message_thread, daemon=True).start()

    def send_file(self):
        """Send a file."""
        if not self.current_room:
            return

        # Open file dialog
        file_path = filedialog.askopenfilename(
            title="Select file to send",
            filetypes=[
                ("All files", "*.*"),
                ("Images", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("Documents", "*.pdf *.doc *.docx *.txt"),
                ("Archives", "*.zip *.rar *.7z")
            ]
        )

        if not file_path:
            return

        # Check file size (limit to 10MB)
        file_size = os.path.getsize(file_path)
        if file_size > 10 * 1024 * 1024:  # 10MB
            messagebox.showerror("Error", "File size must be less than 10MB")
            return

        # Show upload progress
        self.show_status("Uploading file...", "blue")
        self.file_button.config(state="disabled", text="Uploading...")

        def upload_file_thread():
            try:
                # Upload file
                upload_result = self.api_client.upload_file(file_path)

                if upload_result:
                    # Send message with file info
                    file_message = f"📎 {upload_result['file_name']}"
                    message_result = self.api_client.send_message(
                        self.current_room['id'],
                        file_message,
                        upload_result['message_type']
                    )

                    if message_result:
                        self.parent.after(0, lambda: self.show_status("File sent successfully!", "green"))
                    else:
                        self.parent.after(0, lambda: self.show_status("Failed to send file message", "red"))
                else:
                    self.parent.after(0, lambda: self.show_status("File upload failed", "red"))

            except Exception as e:
                self.parent.after(0, lambda: self.show_status(f"Upload error: {e}", "red"))

            finally:
                self.parent.after(0, lambda: self.file_button.config(state="normal", text="📎"))

        threading.Thread(target=upload_file_thread, daemon=True).start()

    def on_typing(self, event=None):
        """Handle typing event."""
        if not self.current_room or not self.ws_client:
            return

        # Start typing indicator
        self.ws_client.start_typing(self.current_room['id'])

        # Reset typing timer
        if self.typing_timer:
            self.parent.after_cancel(self.typing_timer)

        # Stop typing after 3 seconds of inactivity
        self.typing_timer = self.parent.after(3000, self.stop_typing_indicator)

    def on_typing_stop(self, event=None):
        """Handle typing stop event."""
        # Reset timer on key release
        if self.typing_timer:
            self.parent.after_cancel(self.typing_timer)
        self.typing_timer = self.parent.after(1000, self.stop_typing_indicator)

    def stop_typing_indicator(self):
        """Stop typing indicator."""
        if self.current_room and self.ws_client:
            self.ws_client.stop_typing(self.current_room['id'])
        self.typing_timer = None

    def handle_websocket_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket message."""
        try:
            message_type = data.get('type')

            if message_type == 'message':
                self.handle_new_message(data.get('data', {}))
            elif message_type == 'typing_start':
                self.handle_typing_start(data)
            elif message_type == 'typing_stop':
                self.handle_typing_stop(data)
            elif message_type == 'user_joined':
                self.handle_user_joined(data)
            elif message_type == 'user_left':
                self.handle_user_left(data)
            elif message_type == 'user_online':
                self.handle_user_online(data)
            elif message_type == 'user_offline':
                self.handle_user_offline(data)
            elif message_type == 'error':
                self.handle_websocket_error(data)

        except Exception as e:
            print(f"Error handling WebSocket message: {e}")

    def handle_new_message(self, message_data: Dict[str, Any]):
        """Handle new message from WebSocket."""
        if not self.current_room or message_data.get('chat_room_id') != self.current_room['id']:
            return

        # Add message to display
        self.parent.after(0, lambda: self.add_message_to_display(message_data))

    def handle_typing_start(self, data: Dict[str, Any]):
        """Handle typing start indicator."""
        room_id = data.get('room_id')
        user_id = data.get('user_id')
        username = data.get('username')

        if not self.current_room or room_id != self.current_room['id']:
            return

        if user_id == self.user_info['id']:  # Don't show own typing
            return

        if room_id not in self.typing_users:
            self.typing_users[room_id] = set()

        self.typing_users[room_id].add(username)
        self.parent.after(0, self.update_typing_indicator)

    def handle_typing_stop(self, data: Dict[str, Any]):
        """Handle typing stop indicator."""
        room_id = data.get('room_id')
        user_id = data.get('user_id')
        username = data.get('username')

        if not self.current_room or room_id != self.current_room['id']:
            return

        if user_id == self.user_info['id']:  # Don't show own typing
            return

        if room_id in self.typing_users:
            self.typing_users[room_id].discard(username)

        self.parent.after(0, self.update_typing_indicator)

    def update_typing_indicator(self):
        """Update typing indicator display."""
        if not self.current_room:
            return

        room_id = self.current_room['id']
        typing_users = self.typing_users.get(room_id, set())

        if typing_users:
            if len(typing_users) == 1:
                text = f"{list(typing_users)[0]} is typing..."
            elif len(typing_users) == 2:
                text = f"{' and '.join(typing_users)} are typing..."
            else:
                text = f"{len(typing_users)} people are typing..."
        else:
            text = ""

        self.typing_label.config(text=text)

    def handle_user_joined(self, data: Dict[str, Any]):
        """Handle user joined room."""
        # Could show a system message or update member count
        pass

    def handle_user_left(self, data: Dict[str, Any]):
        """Handle user left room."""
        # Could show a system message or update member count
        pass

    def handle_user_online(self, data: Dict[str, Any]):
        """Handle user came online."""
        user_id = data.get('user_id')
        if user_id:
            self.online_users.add(user_id)
            self.parent.after(0, self.update_online_users)

    def handle_user_offline(self, data: Dict[str, Any]):
        """Handle user went offline."""
        user_id = data.get('user_id')
        if user_id:
            self.online_users.discard(user_id)
            self.parent.after(0, self.update_online_users)

    def handle_websocket_error(self, data: Dict[str, Any]):
        """Handle WebSocket error."""
        error_message = data.get('error', 'Unknown error')
        self.parent.after(0, lambda: self.show_status(f"WebSocket error: {error_message}", "red"))

    def update_online_users(self):
        """Update online users list."""
        # This would require getting user info for online user IDs
        # For now, just show the count
        self.users_listbox.delete(0, tk.END)
        self.users_listbox.insert(tk.END, f"Online users: {len(self.online_users)}")

    def show_new_room_dialog(self):
        """Show dialog to create a new room."""
        dialog = NewRoomDialog(self.parent, self.api_client, self.on_room_created)

    def on_room_created(self, room: Dict[str, Any]):
        """Handle new room creation."""
        self.load_chat_rooms()  # Reload rooms list
        self.show_status(f"Created room: {room['name']}")

    def start_private_chat(self, event=None):
        """Start a private chat with selected user."""
        messagebox.showinfo("Private Chat", "Private chat feature coming soon!")

    def handle_logout(self):
        """Handle logout."""
        if self.ws_client:
            self.ws_client.stop()

        # Logout via API
        self.api_client.logout()

        # Call logout callback
        self.on_logout()

    def show_status(self, message: str, color: str = "black"):
        """Show status message."""
        self.status_label.config(text=message, foreground=color)

        # Clear status after 5 seconds
        self.parent.after(5000, lambda: self.status_label.config(text="Ready", foreground="black"))

    def cleanup(self):
        """Clean up resources."""
        if self.ws_client:
            self.ws_client.stop()

        if self.typing_timer:
            self.parent.after_cancel(self.typing_timer)

    def destroy(self):
        """Destroy the chat window."""
        self.cleanup()
        for widget in self.parent.winfo_children():
            widget.destroy()

class NewRoomDialog:
    """Dialog for creating a new chat room."""

    def __init__(self, parent, api_client: APIClient, on_success: Callable):
        self.api_client = api_client
        self.on_success = on_success

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Create New Room")
        self.dialog.geometry("300x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (300 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (200 // 2)
        self.dialog.geometry(f"300x200+{x}+{y}")

        self.setup_ui()

    def setup_ui(self):
        """Set up dialog UI."""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Room name
        ttk.Label(main_frame, text="Room Name:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.name_var = tk.StringVar()
        name_entry = ttk.Entry(main_frame, textvariable=self.name_var, width=30)
        name_entry.grid(row=1, column=0, pady=(0, 15))
        name_entry.focus()

        # Description
        ttk.Label(main_frame, text="Description (optional):").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.desc_var = tk.StringVar()
        desc_entry = ttk.Entry(main_frame, textvariable=self.desc_var, width=30)
        desc_entry.grid(row=3, column=0, pady=(0, 15))

        # Room type
        self.is_group_var = tk.BooleanVar(value=True)
        group_check = ttk.Checkbutton(main_frame, text="Group chat", variable=self.is_group_var)
        group_check.grid(row=4, column=0, sticky=tk.W, pady=(0, 20))

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, sticky=(tk.W, tk.E))

        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="Create", command=self.create_room).grid(row=0, column=1)

        # Bind Enter key
        name_entry.bind('<Return>', lambda e: self.create_room())
        desc_entry.bind('<Return>', lambda e: self.create_room())

    def create_room(self):
        """Create the room."""
        name = self.name_var.get().strip()
        if not name:
            messagebox.showerror("Error", "Please enter a room name")
            return

        description = self.desc_var.get().strip()
        is_group = self.is_group_var.get()

        def create_room_thread():
            try:
                room = self.api_client.create_chat_room(name, description, is_group)
                if room:
                    self.dialog.after(0, lambda: self.on_success(room))
                    self.dialog.after(0, self.dialog.destroy)
                else:
                    self.dialog.after(0, lambda: messagebox.showerror("Error", "Failed to create room"))
            except Exception as e:
                self.dialog.after(0, lambda: messagebox.showerror("Error", f"Error creating room: {e}"))

        threading.Thread(target=create_room_thread, daemon=True).start()
