import os
import json
import requests
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from pathlib import Path

class TokenManager:
    """Manages JWT tokens for authentication."""
    
    def __init__(self):
        self.token_file = Path.home() / ".chat_app_token.json"
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.expires_at: Optional[datetime] = None
        self.load_token()
    
    def save_token(self, access_token: str, refresh_token: str, expires_in: int = 1800):
        """Save tokens to file."""
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.expires_at = datetime.now() + timedelta(seconds=expires_in)
        
        token_data = {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "expires_at": self.expires_at.isoformat()
        }
        
        try:
            with open(self.token_file, 'w') as f:
                json.dump(token_data, f)
        except Exception as e:
            print(f"Error saving token: {e}")
    
    def load_token(self):
        """Load tokens from file."""
        try:
            if self.token_file.exists():
                with open(self.token_file, 'r') as f:
                    token_data = json.load(f)
                
                self.access_token = token_data.get("access_token")
                self.refresh_token = token_data.get("refresh_token")
                expires_str = token_data.get("expires_at")
                
                if expires_str:
                    self.expires_at = datetime.fromisoformat(expires_str)
        except Exception as e:
            print(f"Error loading token: {e}")
            self.clear_token()
    
    def clear_token(self):
        """Clear tokens."""
        self.access_token = None
        self.refresh_token = None
        self.expires_at = None
        
        try:
            if self.token_file.exists():
                self.token_file.unlink()
        except Exception as e:
            print(f"Error clearing token: {e}")
    
    def has_valid_token(self) -> bool:
        """Check if we have a valid token."""
        if not self.access_token or not self.expires_at:
            return False
        
        # Check if token expires in the next 5 minutes
        return datetime.now() + timedelta(minutes=5) < self.expires_at
    
    def get_access_token(self) -> Optional[str]:
        """Get the current access token."""
        if self.has_valid_token():
            return self.access_token
        return None
    
    def get_refresh_token(self) -> Optional[str]:
        """Get the current refresh token."""
        return self.refresh_token

class APIClient:
    """Handles API communication with the backend."""
    
    def __init__(self):
        self.base_url = os.getenv("API_BASE_URL", "http://localhost:8000")
        self.session = requests.Session()
        self.token_manager: Optional[TokenManager] = None
    
    def set_token_manager(self, token_manager: TokenManager):
        """Set the token manager."""
        self.token_manager = token_manager
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers with authentication."""
        headers = {"Content-Type": "application/json"}
        
        if self.token_manager:
            token = self.token_manager.get_access_token()
            if token:
                headers["Authorization"] = f"Bearer {token}"
        
        return headers
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Optional[Dict[str, Any]]:
        """Make an HTTP request."""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers()
        
        try:
            response = self.session.request(method, url, headers=headers, **kwargs)
            
            if response.status_code == 401 and self.token_manager:
                # Try to refresh token
                if self._refresh_token():
                    headers = self._get_headers()
                    response = self.session.request(method, url, headers=headers, **kwargs)
            
            if response.status_code >= 400:
                print(f"API Error {response.status_code}: {response.text}")
                return None
            
            return response.json()
        
        except requests.exceptions.RequestException as e:
            print(f"Request error: {e}")
            return None
    
    def _refresh_token(self) -> bool:
        """Refresh the access token."""
        if not self.token_manager or not self.token_manager.get_refresh_token():
            return False
        
        try:
            response = self.session.post(
                f"{self.base_url}/auth/refresh",
                json={"refresh_token": self.token_manager.get_refresh_token()},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                token_data = response.json()
                self.token_manager.save_token(
                    token_data["access_token"],
                    token_data["refresh_token"]
                )
                return True
        
        except Exception as e:
            print(f"Token refresh error: {e}")
        
        return False
    
    def login(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Login user."""
        data = {"username": username, "password": password}
        return self._make_request("POST", "/auth/login", json=data)
    
    def register(self, username: str, email: str, password: str, full_name: str = "") -> Optional[Dict[str, Any]]:
        """Register user."""
        data = {
            "username": username,
            "email": email,
            "password": password,
            "full_name": full_name
        }
        return self._make_request("POST", "/auth/register", json=data)
    
    def logout(self) -> bool:
        """Logout user."""
        result = self._make_request("POST", "/auth/logout")
        return result is not None
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """Get current user information."""
        return self._make_request("GET", "/users/me")
    
    def get_users(self) -> List[Dict[str, Any]]:
        """Get list of users."""
        result = self._make_request("GET", "/users")
        return result if result else []
    
    def get_chat_rooms(self) -> List[Dict[str, Any]]:
        """Get user's chat rooms."""
        result = self._make_request("GET", "/chat-rooms")
        return result if result else []
    
    def create_chat_room(self, name: str, description: str = "", is_group: bool = False) -> Optional[Dict[str, Any]]:
        """Create a new chat room."""
        data = {
            "name": name,
            "description": description,
            "is_group": is_group
        }
        return self._make_request("POST", "/chat-rooms", json=data)
    
    def join_chat_room(self, room_id: int) -> bool:
        """Join a chat room."""
        result = self._make_request("POST", f"/chat-rooms/{room_id}/join")
        return result is not None
    
    def leave_chat_room(self, room_id: int) -> bool:
        """Leave a chat room."""
        result = self._make_request("DELETE", f"/chat-rooms/{room_id}/leave")
        return result is not None
    
    def get_chat_room_members(self, room_id: int) -> List[Dict[str, Any]]:
        """Get chat room members."""
        result = self._make_request("GET", f"/chat-rooms/{room_id}/members")
        return result if result else []
    
    def get_messages(self, room_id: int, skip: int = 0, limit: int = 50) -> List[Dict[str, Any]]:
        """Get messages from a chat room."""
        result = self._make_request("GET", f"/chat-rooms/{room_id}/messages?skip={skip}&limit={limit}")
        return result if result else []
    
    def send_message(self, room_id: int, content: str, message_type: str = "text") -> Optional[Dict[str, Any]]:
        """Send a message to a chat room."""
        data = {
            "chat_room_id": room_id,
            "content": content,
            "message_type": message_type
        }
        return self._make_request("POST", f"/chat-rooms/{room_id}/messages", json=data)
    
    def edit_message(self, message_id: int, content: str) -> Optional[Dict[str, Any]]:
        """Edit a message."""
        data = {"content": content}
        return self._make_request("PUT", f"/messages/{message_id}", json=data)
    
    def delete_message(self, message_id: int) -> bool:
        """Delete a message."""
        result = self._make_request("DELETE", f"/messages/{message_id}")
        return result is not None

    def upload_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Upload a file."""
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f)}
                headers = {}

                # Add authorization header
                if self.token_manager:
                    token = self.token_manager.get_access_token()
                    if token:
                        headers["Authorization"] = f"Bearer {token}"

                response = self.session.post(
                    f"{self.base_url}/upload",
                    files=files,
                    headers=headers
                )

                if response.status_code == 200:
                    return response.json()
                else:
                    print(f"Upload failed: {response.status_code} - {response.text}")
                    return None

        except Exception as e:
            print(f"Upload error: {e}")
            return None

    def get_status_updates(self) -> List[Dict[str, Any]]:
        """Get status updates."""
        result = self._make_request("GET", "/status")
        return result if result else []

    def create_status_update(self, status_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a status update."""
        return self._make_request("POST", "/status", json=status_data)

    def view_status_update(self, status_id: int) -> bool:
        """Mark a status update as viewed."""
        result = self._make_request("POST", f"/status/{status_id}/view")
        return result is not None

    def get_contacts(self) -> List[Dict[str, Any]]:
        """Get user's contacts."""
        result = self._make_request("GET", "/contacts")
        return result if result else []

    def add_contact(self, contact_user_id: int, contact_name: str = None) -> Optional[Dict[str, Any]]:
        """Add a new contact."""
        data = {"contact_user_id": contact_user_id}
        if contact_name:
            data["contact_name"] = contact_name
        return self._make_request("POST", "/contacts", json=data)

    def add_message_reaction(self, message_id: int, emoji: str) -> Optional[Dict[str, Any]]:
        """Add reaction to a message."""
        data = {"message_id": message_id, "emoji": emoji}
        return self._make_request("POST", f"/messages/{message_id}/reactions", json=data)

    def reply_to_message(self, message_id: int, content: str) -> Optional[Dict[str, Any]]:
        """Reply to a message."""
        data = {"message_id": message_id, "content": content}
        return self._make_request("POST", f"/messages/{message_id}/reply", json=data)

    def forward_messages(self, message_ids: List[int], target_chat_room_id: int) -> Optional[List[Dict[str, Any]]]:
        """Forward messages to another chat room."""
        data = {"message_ids": message_ids, "target_chat_room_id": target_chat_room_id}
        return self._make_request("POST", "/messages/forward", json=data)

def format_timestamp(timestamp_str: str) -> str:
    """Format timestamp for display."""
    try:
        dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        now = datetime.now(dt.tzinfo)
        
        if dt.date() == now.date():
            return dt.strftime("%H:%M")
        elif dt.date() == (now - timedelta(days=1)).date():
            return f"Yesterday {dt.strftime('%H:%M')}"
        else:
            return dt.strftime("%m/%d %H:%M")
    except:
        return timestamp_str

def truncate_text(text: str, max_length: int = 50) -> str:
    """Truncate text to specified length."""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."
