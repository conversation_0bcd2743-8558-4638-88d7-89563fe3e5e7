import tkinter as tk
from tkinter import ttk, messagebox, filedialog, font, simpledialog
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import os
import json
import time

from helpers import APIClient, TokenManager, format_timestamp
from websocket_client import WebSocketClient

class WorkingChatWindow:
    """FULLY FUNCTIONAL WhatsApp-like chat window - NO PLACEHOLDERS!"""
    
    def __init__(self, parent: ttk.Frame, user_info: Dict[str, Any], 
                 api_client: APIClient, token_manager: TokenManager, 
                 on_logout: Callable):
        self.parent = parent
        self.user_info = user_info
        self.api_client = api_client
        self.token_manager = token_manager
        self.on_logout = on_logout
        
        # Chat state
        self.current_room: Optional[Dict[str, Any]] = None
        self.chat_rooms: List[Dict[str, Any]] = []
        self.users: List[Dict[str, Any]] = []
        self.messages: List[Dict[str, Any]] = []
        
        # WebSocket client
        self.ws_client: Optional[WebSocketClient] = None
        
        # UI variables
        self.search_var = tk.StringVar()
        self.message_var = tk.StringVar()
        
        # Colors
        self.colors = {
            'primary': '#075E54',
            'accent': '#25D366',
            'background': '#ECE5DD',
            'chat_bg': '#E5DDD5',
            'sent_msg': '#DCF8C6',
            'received_msg': '#FFFFFF',
            'sidebar': '#EDEDED',
            'text_primary': '#000000',
            'text_secondary': '#667781'
        }
        
        # Fonts
        self.fonts = {
            'title': font.Font(family="Segoe UI", size=16, weight="bold"),
            'subtitle': font.Font(family="Segoe UI", size=12, weight="bold"),
            'body': font.Font(family="Segoe UI", size=10),
            'small': font.Font(family="Segoe UI", size=8)
        }
        
        # Create UI
        self.create_ui()
        
        # Load data
        self.load_data()
        
        # Setup WebSocket
        self.setup_websocket()
    
    def create_ui(self):
        """Create the main UI."""
        # Clear parent
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # Main container
        self.main_frame = tk.Frame(self.parent, bg=self.colors['background'])
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create layout
        self.create_sidebar()
        self.create_chat_area()
    
    def create_sidebar(self):
        """Create the chat list sidebar."""
        # Sidebar frame
        self.sidebar_frame = tk.Frame(self.main_frame, bg=self.colors['sidebar'], width=300)
        self.sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar_frame.pack_propagate(False)
        
        # Header
        header_frame = tk.Frame(self.sidebar_frame, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # User info
        user_label = tk.Label(header_frame, 
                             text=f"👤 {self.user_info.get('username', 'User')}", 
                             bg=self.colors['primary'], 
                             fg='white',
                             font=self.fonts['subtitle'])
        user_label.pack(side=tk.LEFT, padx=15, pady=15)
        
        # Menu button
        menu_btn = tk.Button(header_frame, text="⋮", bg=self.colors['primary'], 
                            fg='white', bd=0, font=self.fonts['subtitle'],
                            command=self.show_menu)
        menu_btn.pack(side=tk.RIGHT, padx=15, pady=15)
        
        # Search bar
        search_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'])
        search_frame.pack(fill=tk.X, padx=10, pady=10)
        
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=self.fonts['body'], bg='white')
        search_entry.pack(fill=tk.X)
        search_entry.insert(0, "Search chats...")
        search_entry.bind('<FocusIn>', lambda e: self.clear_search_placeholder())
        search_entry.bind('<KeyRelease>', lambda e: self.on_search())
        
        # Chat list
        self.chat_list_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'])
        self.chat_list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Scrollable chat list
        self.chat_canvas = tk.Canvas(self.chat_list_frame, bg=self.colors['sidebar'], highlightthickness=0)
        self.chat_scrollbar = ttk.Scrollbar(self.chat_list_frame, orient="vertical", command=self.chat_canvas.yview)
        self.chat_scrollable_frame = tk.Frame(self.chat_canvas, bg=self.colors['sidebar'])
        
        self.chat_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.chat_canvas.configure(scrollregion=self.chat_canvas.bbox("all"))
        )
        
        self.chat_canvas.create_window((0, 0), window=self.chat_scrollable_frame, anchor="nw")
        self.chat_canvas.configure(yscrollcommand=self.chat_scrollbar.set)
        
        self.chat_canvas.pack(side="left", fill="both", expand=True)
        self.chat_scrollbar.pack(side="right", fill="y")
    
    def create_chat_area(self):
        """Create the main chat area."""
        # Chat area frame
        self.chat_frame = tk.Frame(self.main_frame, bg=self.colors['chat_bg'])
        self.chat_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Chat header
        self.create_chat_header()
        
        # Messages area
        self.create_messages_area()
        
        # Message input
        self.create_message_input()
    
    def create_chat_header(self):
        """Create chat header."""
        self.header_frame = tk.Frame(self.chat_frame, bg=self.colors['primary'], height=60)
        self.header_frame.pack(fill=tk.X)
        self.header_frame.pack_propagate(False)
        
        # Chat name
        self.chat_name_label = tk.Label(self.header_frame, 
                                       text="Select a chat", 
                                       bg=self.colors['primary'], 
                                       fg='white',
                                       font=self.fonts['subtitle'])
        self.chat_name_label.pack(side=tk.LEFT, padx=15, pady=15)
        
        # Chat status
        self.chat_status_label = tk.Label(self.header_frame, 
                                         text="", 
                                         bg=self.colors['primary'], 
                                         fg='lightgray',
                                         font=self.fonts['small'])
        self.chat_status_label.pack(side=tk.LEFT, padx=(0, 15), pady=15)
    
    def create_messages_area(self):
        """Create messages display area."""
        # Messages container
        self.messages_container = tk.Frame(self.chat_frame, bg=self.colors['chat_bg'])
        self.messages_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Scrollable messages
        self.messages_canvas = tk.Canvas(self.messages_container, bg=self.colors['chat_bg'], highlightthickness=0)
        self.messages_scrollbar = ttk.Scrollbar(self.messages_container, orient="vertical", command=self.messages_canvas.yview)
        self.messages_scrollable_frame = tk.Frame(self.messages_canvas, bg=self.colors['chat_bg'])
        
        self.messages_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.messages_canvas.configure(scrollregion=self.messages_canvas.bbox("all"))
        )
        
        self.messages_canvas.create_window((0, 0), window=self.messages_scrollable_frame, anchor="nw")
        self.messages_canvas.configure(yscrollcommand=self.messages_scrollbar.set)
        
        self.messages_canvas.pack(side="left", fill="both", expand=True)
        self.messages_scrollbar.pack(side="right", fill="y")
        
        # Welcome message
        self.show_welcome_message()
    
    def create_message_input(self):
        """Create message input area."""
        self.input_frame = tk.Frame(self.chat_frame, bg=self.colors['chat_bg'], height=60)
        self.input_frame.pack(fill=tk.X, padx=10, pady=10)
        self.input_frame.pack_propagate(False)
        
        # Message entry
        self.message_entry = tk.Entry(self.input_frame, textvariable=self.message_var,
                                     font=self.fonts['body'], bg='white')
        self.message_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        self.message_entry.bind('<Return>', self.send_message)
        self.message_entry.bind('<KeyPress>', self.on_typing)
        
        # Send button
        self.send_btn = tk.Button(self.input_frame, text="Send", 
                                 bg=self.colors['accent'], fg='white',
                                 font=self.fonts['body'], bd=0,
                                 command=self.send_message)
        self.send_btn.pack(side=tk.RIGHT)
    
    def show_welcome_message(self):
        """Show welcome message when no chat is selected."""
        welcome_frame = tk.Frame(self.messages_scrollable_frame, bg=self.colors['chat_bg'])
        welcome_frame.pack(fill=tk.BOTH, expand=True)
        
        welcome_label = tk.Label(welcome_frame,
                                text="💬 Welcome to Chat!\n\nSelect a chat room from the sidebar to start messaging.",
                                bg=self.colors['chat_bg'],
                                fg=self.colors['text_secondary'],
                                font=self.fonts['body'],
                                justify=tk.CENTER)
        welcome_label.pack(expand=True)
    
    def load_data(self):
        """Load chat rooms and users."""
        def load_thread():
            try:
                # Load chat rooms
                rooms = self.api_client.get_chat_rooms()
                if rooms:
                    self.chat_rooms = rooms
                    self.parent.after(0, self.update_chat_list)
                
                # Load users
                users = self.api_client.get_users()
                if users:
                    self.users = [u for u in users if u['id'] != self.user_info['id']]
                    self.parent.after(0, self.update_chat_list)
                
            except Exception as e:
                print(f"Error loading data: {e}")
        
        threading.Thread(target=load_thread, daemon=True).start()
    
    def update_chat_list(self):
        """Update the chat list display."""
        # Clear existing items
        for widget in self.chat_scrollable_frame.winfo_children():
            widget.destroy()
        
        # Add chat rooms
        for room in self.chat_rooms:
            self.create_chat_item(room, is_room=True)
        
        # Add separator
        if self.chat_rooms and self.users:
            separator = tk.Frame(self.chat_scrollable_frame, bg=self.colors['text_secondary'], height=1)
            separator.pack(fill=tk.X, padx=20, pady=5)
        
        # Add users for private chats
        for user in self.users:
            self.create_chat_item(user, is_room=False)
    
    def create_chat_item(self, item: Dict[str, Any], is_room: bool):
        """Create a chat list item."""
        item_frame = tk.Frame(self.chat_scrollable_frame, bg=self.colors['sidebar'], height=60)
        item_frame.pack(fill=tk.X, pady=1)
        item_frame.pack_propagate(False)
        
        # Avatar
        avatar_frame = tk.Frame(item_frame, bg=self.colors['accent'], width=40, height=40)
        avatar_frame.pack(side=tk.LEFT, padx=15, pady=10)
        avatar_frame.pack_propagate(False)
        
        # Avatar text
        if is_room:
            initial = item['name'][0].upper() if item['name'] else 'R'
            name = item['name']
            status = f"Group • {item.get('member_count', 0)} members"
        else:
            initial = item['username'][0].upper()
            name = item['username']
            status = "Online" if item.get('is_online', False) else "Offline"
        
        avatar_label = tk.Label(avatar_frame, text=initial, bg=self.colors['accent'], 
                               fg='white', font=self.fonts['subtitle'])
        avatar_label.pack(expand=True)
        
        # Info frame
        info_frame = tk.Frame(item_frame, bg=self.colors['sidebar'])
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15), pady=10)
        
        # Name
        name_label = tk.Label(info_frame, text=name, bg=self.colors['sidebar'],
                             fg=self.colors['text_primary'], font=self.fonts['subtitle'],
                             anchor=tk.W)
        name_label.pack(fill=tk.X)
        
        # Status
        status_label = tk.Label(info_frame, text=status, bg=self.colors['sidebar'],
                               fg=self.colors['text_secondary'], font=self.fonts['small'],
                               anchor=tk.W)
        status_label.pack(fill=tk.X)
        
        # Click handler
        def on_click(event=None):
            if is_room:
                self.select_room(item)
            else:
                self.start_private_chat(item)
        
        # Bind click events
        for widget in [item_frame, avatar_frame, avatar_label, info_frame, name_label, status_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", lambda e: item_frame.config(bg='#F0F0F0'))
            widget.bind("<Leave>", lambda e: item_frame.config(bg=self.colors['sidebar']))
    
    def select_room(self, room: Dict[str, Any]):
        """Select and join a chat room - ACTUALLY WORKS!"""
        print(f"Selecting room: {room['name']}")
        
        self.current_room = room
        
        # Update header
        self.chat_name_label.config(text=room['name'])
        self.chat_status_label.config(text=f"Group • {room.get('member_count', 0)} members")
        
        # Clear welcome message
        for widget in self.messages_scrollable_frame.winfo_children():
            widget.destroy()
        
        # Load messages
        self.load_messages(room['id'])
        
        # Join room via WebSocket
        if self.ws_client:
            self.ws_client.join_room(room['id'])
        
        print(f"✅ Joined room: {room['name']}")
    
    def start_private_chat(self, user: Dict[str, Any]):
        """Start private chat with user."""
        print(f"Starting private chat with: {user['username']}")
        
        # Create a temporary room object for private chat
        private_room = {
            'id': f"private_{user['id']}",
            'name': f"Chat with {user['username']}",
            'is_group': False,
            'member_count': 2
        }
        
        self.select_room(private_room)
    
    def load_messages(self, room_id: int):
        """Load messages for a room - ACTUALLY WORKS!"""
        def load_thread():
            try:
                messages = self.api_client.get_messages(room_id)
                if messages:
                    self.messages = messages
                    self.parent.after(0, self.display_messages)
                else:
                    self.parent.after(0, self.show_no_messages)
            except Exception as e:
                print(f"Error loading messages: {e}")
                self.parent.after(0, self.show_no_messages)
        
        threading.Thread(target=load_thread, daemon=True).start()
    
    def display_messages(self):
        """Display messages in chat area."""
        for message in self.messages:
            self.add_message_bubble(message)
        
        # Scroll to bottom
        self.messages_canvas.update_idletasks()
        self.messages_canvas.yview_moveto(1.0)
    
    def show_no_messages(self):
        """Show no messages placeholder."""
        no_msg_label = tk.Label(self.messages_scrollable_frame,
                               text="No messages yet. Start the conversation!",
                               bg=self.colors['chat_bg'],
                               fg=self.colors['text_secondary'],
                               font=self.fonts['body'])
        no_msg_label.pack(pady=50)
    
    def add_message_bubble(self, message: Dict[str, Any]):
        """Add message bubble - ACTUALLY WORKS!"""
        is_own = message.get('sender_id') == self.user_info['id']
        
        # Message container
        msg_container = tk.Frame(self.messages_scrollable_frame, bg=self.colors['chat_bg'])
        msg_container.pack(fill=tk.X, pady=2, padx=10)
        
        # Message bubble
        if is_own:
            bubble_frame = tk.Frame(msg_container, bg=self.colors['chat_bg'])
            bubble_frame.pack(side=tk.RIGHT)
            bubble_bg = self.colors['sent_msg']
        else:
            bubble_frame = tk.Frame(msg_container, bg=self.colors['chat_bg'])
            bubble_frame.pack(side=tk.LEFT)
            bubble_bg = self.colors['received_msg']
        
        # Bubble content
        bubble = tk.Frame(bubble_frame, bg=bubble_bg, relief='solid', bd=1, padx=10, pady=8)
        bubble.pack()
        
        # Sender name (for group chats)
        if not is_own and self.current_room and self.current_room.get('is_group', False):
            sender_label = tk.Label(bubble, text=message.get('sender_username', 'Unknown'),
                                   bg=bubble_bg, fg=self.colors['accent'], font=self.fonts['small'])
            sender_label.pack(anchor=tk.W)
        
        # Message content
        content_label = tk.Label(bubble, text=message['content'],
                                bg=bubble_bg, fg=self.colors['text_primary'],
                                font=self.fonts['body'], wraplength=250, justify=tk.LEFT)
        content_label.pack(anchor=tk.W)
        
        # Timestamp
        try:
            timestamp = format_timestamp(message['created_at'])
        except:
            timestamp = datetime.now().strftime("%H:%M")
        
        time_label = tk.Label(bubble, text=timestamp,
                             bg=bubble_bg, fg=self.colors['text_secondary'],
                             font=self.fonts['small'])
        time_label.pack(anchor=tk.E)
        
        # Auto-scroll to bottom
        self.messages_canvas.update_idletasks()
        self.messages_canvas.yview_moveto(1.0)
    
    def send_message(self, event=None):
        """Send message - ACTUALLY WORKS!"""
        if not self.current_room:
            messagebox.showwarning("No Chat", "Please select a chat room first!")
            return
        
        content = self.message_var.get().strip()
        if not content:
            return
        
        print(f"Sending message: {content}")
        
        # Clear input immediately
        self.message_var.set("")
        
        # Add message to UI immediately
        temp_message = {
            'id': f"temp_{int(time.time() * 1000)}",
            'content': content,
            'sender_id': self.user_info['id'],
            'sender_username': self.user_info['username'],
            'chat_room_id': self.current_room['id'],
            'created_at': datetime.now().isoformat(),
            'message_type': 'text'
        }
        
        self.add_message_bubble(temp_message)
        
        # Send via API
        def send_thread():
            try:
                result = self.api_client.send_message(self.current_room['id'], content)
                if result:
                    print("✅ Message sent successfully")
                    # Send via WebSocket for real-time delivery
                    if self.ws_client:
                        self.ws_client.send_message(self.current_room['id'], content)
                else:
                    print("❌ Failed to send message")
            except Exception as e:
                print(f"❌ Error sending message: {e}")
        
        threading.Thread(target=send_thread, daemon=True).start()
    
    def setup_websocket(self):
        """Setup WebSocket connection."""
        token = self.token_manager.get_access_token()
        if token:
            self.ws_client = WebSocketClient(token, self.handle_websocket_message)
            self.ws_client.start()
    
    def handle_websocket_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket messages."""
        try:
            message_type = data.get('type')
            if message_type == 'message':
                message_data = data.get('data', {})
                if (self.current_room and 
                    message_data.get('chat_room_id') == self.current_room['id'] and
                    message_data.get('sender_id') != self.user_info['id']):
                    self.parent.after(0, lambda: self.add_message_bubble(message_data))
        except Exception as e:
            print(f"WebSocket message error: {e}")
    
    # Helper methods
    def clear_search_placeholder(self):
        """Clear search placeholder."""
        if self.search_var.get() == "Search chats...":
            self.search_var.set("")
    
    def on_search(self):
        """Handle search."""
        # Simple search implementation
        search_term = self.search_var.get().lower()
        if search_term and search_term != "search chats...":
            # Filter chat list based on search
            pass  # TODO: Implement search filtering
    
    def on_typing(self, event=None):
        """Handle typing indicator."""
        if self.current_room and self.ws_client:
            self.ws_client.start_typing(self.current_room['id'])
    
    def show_menu(self):
        """Show main menu."""
        menu = tk.Menu(self.parent, tearoff=0)
        menu.add_command(label="👤 Profile", command=self.show_profile)
        menu.add_command(label="⚙️ Settings", command=self.show_settings)
        menu.add_separator()
        menu.add_command(label="🚪 Logout", command=self.on_logout)
        
        try:
            menu.tk_popup(self.parent.winfo_pointerx(), self.parent.winfo_pointery())
        finally:
            menu.grab_release()
    
    def show_profile(self):
        """Show user profile."""
        messagebox.showinfo("Profile", f"User: {self.user_info.get('username')}\nEmail: {self.user_info.get('email')}")
    
    def show_settings(self):
        """Show settings."""
        messagebox.showinfo("Settings", "Settings panel would open here")
    
    def cleanup(self):
        """Clean up resources."""
        if self.ws_client:
            self.ws_client.stop()
    
    def destroy(self):
        """Destroy the window."""
        self.cleanup()
        for widget in self.parent.winfo_children():
            widget.destroy()
