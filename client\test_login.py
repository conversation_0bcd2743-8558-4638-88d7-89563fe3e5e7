#!/usr/bin/env python3
"""
Test script for client login functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from helpers import APIClient, <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_client_login():
    """Test client login functionality."""
    print("🧪 Testing Client Login Functionality")
    print("=" * 50)
    
    # Initialize API client and token manager
    api_client = APIClient()
    token_manager = TokenManager()
    api_client.set_token_manager(token_manager)
    
    # Test 1: Registration
    print("\n1. Testing Registration...")
    result = api_client.register(
        username="clienttest123",
        email="<EMAIL>",
        password="testpass123",
        full_name="Client Test User"
    )
    
    if result:
        print("✅ Registration successful!")
        print(f"User ID: {result.get('id')}")
        print(f"Username: {result.get('username')}")
    else:
        print("❌ Registration failed (might already exist)")
    
    # Test 2: Login
    print("\n2. Testing Login...")
    login_result = api_client.login("clienttest123", "testpass123")
    
    if login_result:
        print("✅ Login successful!")
        print(f"Access Token: {login_result.get('access_token', '')[:50]}...")
        
        # Save tokens
        token_manager.save_token(
            login_result["access_token"],
            login_result["refresh_token"]
        )
        
        # Test 3: Get current user
        print("\n3. Testing Get Current User...")
        user_info = api_client.get_current_user()
        
        if user_info:
            print("✅ Get current user successful!")
            print(f"User: {user_info.get('username')} ({user_info.get('full_name')})")
            print(f"Email: {user_info.get('email')}")
            print(f"Active: {user_info.get('is_active')}")
        else:
            print("❌ Get current user failed")
            
    else:
        print("❌ Login failed")
    
    # Test 4: Invalid login
    print("\n4. Testing Invalid Login...")
    invalid_result = api_client.login("clienttest123", "wrongpassword")
    
    if not invalid_result:
        print("✅ Invalid login correctly rejected!")
    else:
        print("❌ Invalid login should have failed")
    
    print("\n🎉 Client login tests completed!")

if __name__ == "__main__":
    test_client_login()
