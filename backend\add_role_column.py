import sqlite3

conn = sqlite3.connect('chat_app.db')
cursor = conn.cursor()

try:
    cursor.execute('ALTER TABLE chat_members ADD COLUMN role VARCHAR(20) DEFAULT "member"')
    conn.commit()
    print('✅ Added role column to chat_members')
except Exception as e:
    if 'duplicate column name' in str(e):
        print('✅ Role column already exists')
    else:
        print(f'❌ Error: {e}')
finally:
    conn.close()
