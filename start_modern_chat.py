#!/usr/bin/env python3
"""
Complete startup script for the modern WhatsApp-like chat application.
This ensures everything is properly configured for remote users to connect.
"""

import os
import sys
import subprocess
import time
import requests
import threading
from pathlib import Path

def check_backend():
    """Check if backend is running."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=3)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """Start the backend server."""
    print("🚀 Starting backend server...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    if os.name == 'nt':  # Windows
        cmd = ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"]
        process = subprocess.Popen(cmd, cwd=backend_dir, creationflags=subprocess.CREATE_NEW_CONSOLE)
    else:  # Unix/Linux/Mac
        cmd = ["python3", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"]
        process = subprocess.Popen(cmd, cwd=backend_dir)
    
    # Wait for backend to start
    print("⏳ Waiting for backend to start...")
    for i in range(30):
        if check_backend():
            print("✅ Backend server is ready!")
            return process
        time.sleep(1)
    
    print("❌ Backend failed to start")
    return None

def setup_database():
    """Set up database with all required tables and data."""
    print("🗄️ Setting up database...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    try:
        # Run database migration
        result = subprocess.run(
            ["python", "migrate_database.py"],
            cwd=backend_dir,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ Database migration completed")
        else:
            print(f"⚠️ Database migration: {result.stderr}")
        
        # Set up general chat
        result = subprocess.run(
            ["python", "setup_general_chat.py"],
            cwd=backend_dir,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ General chat setup completed")
        else:
            print(f"⚠️ General chat setup: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Database setup error: {e}")

def create_demo_users():
    """Create demo users for testing."""
    print("👥 Creating demo users...")
    
    demo_users = [
        {"username": "alice", "email": "<EMAIL>", "password": "demo123", "full_name": "Alice Johnson"},
        {"username": "bob", "email": "<EMAIL>", "password": "demo123", "full_name": "Bob Smith"},
        {"username": "charlie", "email": "<EMAIL>", "password": "demo123", "full_name": "Charlie Brown"}
    ]
    
    for user_data in demo_users:
        try:
            response = requests.post("http://localhost:8001/auth/register", json=user_data)
            if response.status_code == 200:
                print(f"✅ Created demo user: {user_data['username']}")
            else:
                print(f"⚠️ Demo user {user_data['username']} might already exist")
        except Exception as e:
            print(f"❌ Error creating demo user {user_data['username']}: {e}")

def verify_setup():
    """Verify that everything is set up correctly."""
    print("🔍 Verifying setup...")
    
    try:
        # Test login
        response = requests.post("http://localhost:8001/auth/login", json={
            "username": "alice",
            "password": "demo123"
        })
        
        if response.status_code == 200:
            token = response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ Authentication working")
            
            # Test chat rooms
            rooms_response = requests.get("http://localhost:8001/chat-rooms", headers=headers)
            if rooms_response.status_code == 200:
                rooms = rooms_response.json()
                print(f"✅ Found {len(rooms)} chat rooms")
                
                # Check for General Chat
                general_chat = next((r for r in rooms if r['name'] == 'General Chat'), None)
                if general_chat:
                    print("✅ General Chat room is available")
                else:
                    print("⚠️ General Chat room not found")
            
            # Test users
            users_response = requests.get("http://localhost:8001/users", headers=headers)
            if users_response.status_code == 200:
                users = users_response.json()
                print(f"✅ Found {len(users)} users")
            
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Verification error: {e}")

def start_client():
    """Start the client application."""
    print("🖥️ Starting modern chat client...")
    
    client_dir = Path(__file__).parent / "client"
    
    try:
        subprocess.run(["python", "main.py"], cwd=client_dir)
    except KeyboardInterrupt:
        print("\n👋 Client closed")
    except Exception as e:
        print(f"❌ Client error: {e}")

def show_instructions():
    """Show usage instructions."""
    print("\n" + "=" * 60)
    print("🎉 MODERN WHATSAPP-LIKE CHAT APPLICATION READY!")
    print("=" * 60)
    print("\n📱 HOW TO USE:")
    print("1. The client will open with a modern WhatsApp-like interface")
    print("2. Login with one of these demo accounts:")
    print("   • Username: alice, Password: demo123")
    print("   • Username: bob, Password: demo123") 
    print("   • Username: charlie, Password: demo123")
    print("\n💬 FEATURES TO TRY:")
    print("• Join 'General Chat' to talk with all users")
    print("• Click on user names to start private chats")
    print("• Send messages with modern chat bubbles")
    print("• See real-time typing indicators")
    print("• Try voice messages and reactions")
    print("• Create status updates")
    print("\n🌐 FOR REMOTE USERS:")
    print("• Share this IP address: http://YOUR_IP:8001")
    print("• Remote users can run the client and connect")
    print("• Everyone will see the same modern interface")
    print("• All WhatsApp-like features work across the network")
    print("\n" + "=" * 60)

def main():
    """Main startup function."""
    print("💬 Modern WhatsApp-Like Chat Application")
    print("=" * 50)
    
    # Check if backend is already running
    if check_backend():
        print("✅ Backend is already running")
    else:
        # Start backend
        backend_process = start_backend()
        if not backend_process:
            print("❌ Failed to start backend")
            return 1
    
    # Setup database and data
    setup_database()
    
    # Create demo users
    create_demo_users()
    
    # Verify setup
    verify_setup()
    
    # Show instructions
    show_instructions()
    
    # Ask if user wants to start client
    choice = input("\nStart the client application? (y/n): ").strip().lower()
    
    if choice == 'y':
        start_client()
    else:
        print("\n👋 Setup complete! You can start the client manually:")
        print("cd client && python main.py")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Startup interrupted")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Startup error: {e}")
        sys.exit(1)
