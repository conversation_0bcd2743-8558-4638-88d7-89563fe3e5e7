import tkinter as tk
from tkinter import ttk, messagebox
import asyncio
import threading
from typing import Optional
import os
from dotenv import load_dotenv

from login import LoginWindow
from chat_window import ChatWindow
from helpers import TokenManager, APIClient

# Load environment variables
load_dotenv()

class ChatApplication:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Real-Time Chat Application")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Initialize components
        self.token_manager = TokenManager()
        self.api_client = APIClient()
        self.chat_window: Optional[ChatWindow] = None
        self.login_window: Optional[LoginWindow] = None
        
        # Set up the application
        self.setup_application()
        
        # Check if user is already logged in
        if self.token_manager.has_valid_token():
            self.show_chat_window()
        else:
            self.show_login_window()
    
    def center_window(self):
        """Center the window on the screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_application(self):
        """Set up the main application."""
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Set up the main frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.rowconfigure(0, weight=1)
        
        # Handle window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def show_login_window(self):
        """Show the login window."""
        if self.chat_window:
            self.chat_window.destroy()
            self.chat_window = None
        
        self.login_window = LoginWindow(
            self.main_frame,
            on_login_success=self.on_login_success,
            api_client=self.api_client,
            token_manager=self.token_manager
        )
        
        # Update window title and size
        self.root.title("Chat Application - Login")
        self.root.geometry("400x500")
        self.center_window()
    
    def show_chat_window(self):
        """Show the chat window."""
        if self.login_window:
            self.login_window.destroy()
            self.login_window = None
        
        # Get user info
        user_info = self.api_client.get_current_user()
        if not user_info:
            messagebox.showerror("Error", "Failed to get user information")
            self.show_login_window()
            return
        
        self.chat_window = ChatWindow(
            self.main_frame,
            user_info=user_info,
            api_client=self.api_client,
            token_manager=self.token_manager,
            on_logout=self.on_logout
        )
        
        # Update window title and size
        self.root.title(f"Chat Application - {user_info['username']}")
        self.root.geometry("1000x700")
        self.center_window()
    
    def on_login_success(self, user_info: dict):
        """Handle successful login."""
        self.show_chat_window()
    
    def on_logout(self):
        """Handle logout."""
        self.token_manager.clear_token()
        self.show_login_window()
    
    def on_closing(self):
        """Handle application closing."""
        if self.chat_window:
            self.chat_window.cleanup()
        
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """Run the application."""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()

def main():
    """Main entry point."""
    # Set up asyncio event loop for Windows
    if os.name == 'nt':  # Windows
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # Create and run the application
    app = ChatApplication()
    app.run()

if __name__ == "__main__":
    main()
