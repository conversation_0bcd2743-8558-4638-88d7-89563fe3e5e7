#!/usr/bin/env python3
"""
Development startup script for the Real-Time Chat Application.
This script helps start both backend and client for local development.
"""

import os
import sys
import subprocess
import time
import threading
import signal
from pathlib import Path

class ChatAppDev:
    def __init__(self):
        self.backend_process = None
        self.client_process = None
        self.running = True
        
        # Paths
        self.root_dir = Path(__file__).parent
        self.backend_dir = self.root_dir / "backend"
        self.client_dir = self.root_dir / "client"
    
    def check_dependencies(self):
        """Check if required dependencies are installed."""
        print("🔍 Checking dependencies...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8 or higher is required")
            return False
        
        # Check if backend dependencies are installed
        try:
            import fastapi
            import uvicorn
            import sqlalchemy
            print("✅ Backend dependencies found")
        except ImportError as e:
            print(f"❌ Backend dependencies missing: {e}")
            print("💡 Run: cd backend && pip install -r requirements.txt")
            return False
        
        # Check if client dependencies are installed
        try:
            import tkinter
            import requests
            import websockets
            print("✅ Client dependencies found")
        except ImportError as e:
            print(f"❌ Client dependencies missing: {e}")
            print("💡 Run: cd client && pip install -r requirements.txt")
            return False
        
        return True
    
    def setup_environment(self):
        """Set up environment variables and configuration."""
        print("⚙️  Setting up environment...")
        
        # Create backend .env if it doesn't exist
        backend_env = self.backend_dir / ".env"
        if not backend_env.exists():
            print("📝 Creating backend .env file...")
            with open(backend_env, "w") as f:
                f.write("SECRET_KEY=dev-secret-key-change-in-production\n")
                f.write("DATABASE_URL=sqlite+aiosqlite:///./chat_app.db\n")
                f.write("DEBUG=True\n")
        
        # Create client .env if it doesn't exist
        client_env = self.client_dir / ".env"
        if not client_env.exists():
            print("📝 Creating client .env file...")
            with open(client_env, "w") as f:
                f.write("API_BASE_URL=http://localhost:8000\n")
        
        print("✅ Environment setup complete")
    
    def start_backend(self):
        """Start the backend server."""
        print("🚀 Starting backend server...")
        
        try:
            # Change to backend directory
            os.chdir(self.backend_dir)
            
            # Start uvicorn server
            cmd = [
                sys.executable, "-m", "uvicorn",
                "app.main:app",
                "--reload",
                "--host", "0.0.0.0",
                "--port", "8000"
            ]
            
            self.backend_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Monitor backend output
            def monitor_backend():
                for line in iter(self.backend_process.stdout.readline, ''):
                    if self.running:
                        print(f"[BACKEND] {line.strip()}")
                    else:
                        break
            
            threading.Thread(target=monitor_backend, daemon=True).start()
            
            # Wait for backend to start
            print("⏳ Waiting for backend to start...")
            time.sleep(3)
            
            # Test backend health
            try:
                import requests
                response = requests.get("http://localhost:8000/health", timeout=5)
                if response.status_code == 200:
                    print("✅ Backend server is running at http://localhost:8000")
                    return True
                else:
                    print(f"❌ Backend health check failed: {response.status_code}")
                    return False
            except Exception as e:
                print(f"❌ Backend health check failed: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False
    
    def start_client(self):
        """Start the client application."""
        print("🖥️  Starting client application...")
        
        try:
            # Change to client directory
            os.chdir(self.client_dir)
            
            # Start client
            cmd = [sys.executable, "main.py"]
            
            self.client_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Monitor client output
            def monitor_client():
                for line in iter(self.client_process.stdout.readline, ''):
                    if self.running:
                        print(f"[CLIENT] {line.strip()}")
                    else:
                        break
            
            threading.Thread(target=monitor_client, daemon=True).start()
            
            print("✅ Client application started")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start client: {e}")
            return False
    
    def stop_services(self):
        """Stop all services."""
        print("\n🛑 Stopping services...")
        self.running = False
        
        if self.client_process:
            try:
                self.client_process.terminate()
                self.client_process.wait(timeout=5)
                print("✅ Client stopped")
            except:
                self.client_process.kill()
                print("🔨 Client force killed")
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ Backend stopped")
            except:
                self.backend_process.kill()
                print("🔨 Backend force killed")
    
    def signal_handler(self, signum, frame):
        """Handle interrupt signals."""
        print(f"\n📡 Received signal {signum}")
        self.stop_services()
        sys.exit(0)
    
    def run(self):
        """Run the development environment."""
        print("🎯 Real-Time Chat Application - Development Mode")
        print("=" * 50)
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # Check dependencies
        if not self.check_dependencies():
            return False
        
        # Setup environment
        self.setup_environment()
        
        # Start backend
        if not self.start_backend():
            return False
        
        # Wait a bit more for backend to fully initialize
        time.sleep(2)
        
        # Start client
        if not self.start_client():
            self.stop_services()
            return False
        
        print("\n🎉 Development environment is ready!")
        print("📋 Available services:")
        print("   • Backend API: http://localhost:8000")
        print("   • API Docs: http://localhost:8000/docs")
        print("   • Health Check: http://localhost:8000/health")
        print("   • Client GUI: Running in separate window")
        print("\n💡 Tips:")
        print("   • Backend auto-reloads on code changes")
        print("   • Check logs above for any errors")
        print("   • Press Ctrl+C to stop all services")
        print("\n⏳ Monitoring services... (Press Ctrl+C to stop)")
        
        try:
            # Keep the main thread alive
            while self.running:
                time.sleep(1)
                
                # Check if processes are still running
                if self.backend_process and self.backend_process.poll() is not None:
                    print("❌ Backend process died unexpectedly")
                    break
                
                if self.client_process and self.client_process.poll() is not None:
                    print("ℹ️  Client process ended")
                    break
        
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_services()
        
        return True

def main():
    """Main entry point."""
    app = ChatAppDev()
    success = app.run()
    
    if success:
        print("👋 Development session ended successfully")
    else:
        print("💥 Development session ended with errors")
        sys.exit(1)

if __name__ == "__main__":
    main()
