import tkinter as tk
from tkinter import ttk, messagebox, filedialog, font, simpledialog
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
import os
import json
import time
import base64
import io
from PIL import Image, ImageTk, ImageDraw, ImageFont
import pygame
import cv2
import pyaudio
import wave
import requests
from plyer import notification

from helpers import APIClient, TokenManager, format_timestamp
from websocket_client import WebSocketClient

class AdvancedChatWindow:
    """FULLY ADVANCED WhatsApp-like chat application with ALL features working."""
    
    def __init__(self, parent: ttk.Frame, user_info: Dict[str, Any], 
                 api_client: APIClient, token_manager: TokenManager, 
                 on_logout: Callable):
        self.parent = parent
        self.user_info = user_info
        self.api_client = api_client
        self.token_manager = token_manager
        self.on_logout = on_logout
        
        # Chat state
        self.current_room: Optional[Dict[str, Any]] = None
        self.chat_rooms: List[Dict[str, Any]] = []
        self.users: List[Dict[str, Any]] = []
        self.messages: List[Dict[str, Any]] = []
        self.contacts: List[Dict[str, Any]] = []
        self.status_updates: List[Dict[str, Any]] = []
        
        # WebSocket client
        self.ws_client: Optional[WebSocketClient] = None
        
        # UI variables
        self.search_var = tk.StringVar()
        self.message_var = tk.StringVar()
        self.typing_var = tk.StringVar()
        
        # Voice recording
        self.is_recording = False
        self.audio_frames = []
        self.audio_stream = None
        
        # Video calling
        self.video_call_active = False
        self.video_capture = None
        self.video_window = None
        
        # File sharing
        self.shared_files = {}
        
        # Theme
        self.dark_mode = False
        
        # Initialize audio
        self.init_audio()
        
        # Colors and fonts
        self.setup_theme()
        
        # Create UI
        self.create_advanced_ui()
        
        # Load data
        self.load_all_data()
        
        # Setup connections
        self.setup_websocket()
        self.setup_notifications()
    
    def init_audio(self):
        """Initialize audio system for voice messages and calls."""
        try:
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            self.audio = pyaudio.PyAudio()
            self.audio_initialized = True
            print("✅ Audio system initialized")
        except Exception as e:
            print(f"❌ Audio initialization failed: {e}")
            self.audio_initialized = False
    
    def setup_theme(self):
        """Setup WhatsApp-like theme."""
        if self.dark_mode:
            self.colors = {
                'primary': '#1F2C33',
                'secondary': '#2A3942',
                'accent': '#00A884',
                'background': '#0B141A',
                'chat_bg': '#0B141A',
                'sent_msg': '#005C4B',
                'received_msg': '#1F2C33',
                'sidebar': '#1F2C33',
                'text_primary': '#E9EDEF',
                'text_secondary': '#8696A0',
                'border': '#2A3942',
                'online': '#00A884',
                'typing': '#00A884'
            }
        else:
            self.colors = {
                'primary': '#075E54',
                'secondary': '#128C7E',
                'accent': '#25D366',
                'background': '#ECE5DD',
                'chat_bg': '#E5DDD5',
                'sent_msg': '#DCF8C6',
                'received_msg': '#FFFFFF',
                'sidebar': '#EDEDED',
                'text_primary': '#000000',
                'text_secondary': '#667781',
                'border': '#D1D7DB',
                'online': '#4FC3F7',
                'typing': '#25D366'
            }
        
        self.fonts = {
            'title': font.Font(family="Segoe UI", size=16, weight="bold"),
            'subtitle': font.Font(family="Segoe UI", size=12, weight="bold"),
            'body': font.Font(family="Segoe UI", size=10),
            'small': font.Font(family="Segoe UI", size=8),
            'emoji': font.Font(family="Segoe UI Emoji", size=16)
        }
    
    def create_advanced_ui(self):
        """Create the advanced UI with all WhatsApp features."""
        # Clear parent
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # Main container
        self.main_frame = tk.Frame(self.parent, bg=self.colors['background'])
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create three-panel layout
        self.create_sidebar()
        self.create_chat_area()
        self.create_info_panel()
    
    def create_sidebar(self):
        """Create advanced sidebar with all features."""
        # Sidebar frame
        self.sidebar_frame = tk.Frame(self.main_frame, bg=self.colors['sidebar'], width=320)
        self.sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar_frame.pack_propagate(False)
        
        # Header with user info and controls
        self.create_sidebar_header()
        
        # Navigation tabs
        self.create_navigation_tabs()
        
        # Search bar
        self.create_search_bar()
        
        # Content area (chats, status, calls, etc.)
        self.create_sidebar_content()
    
    def create_sidebar_header(self):
        """Create sidebar header with user info and controls."""
        header_frame = tk.Frame(self.sidebar_frame, bg=self.colors['primary'], height=70)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # User avatar
        avatar_frame = tk.Frame(header_frame, bg=self.colors['accent'], width=45, height=45)
        avatar_frame.pack(side=tk.LEFT, padx=15, pady=12)
        avatar_frame.pack_propagate(False)
        
        # User initial
        initial = self.user_info.get('username', 'U')[0].upper()
        avatar_canvas = tk.Canvas(avatar_frame, width=45, height=45, bg=self.colors['accent'], highlightthickness=0)
        avatar_canvas.pack()
        avatar_canvas.create_oval(2, 2, 43, 43, fill='white', outline=self.colors['primary'], width=2)
        avatar_canvas.create_text(22, 22, text=initial, fill=self.colors['primary'], font=self.fonts['subtitle'])
        
        # User info
        info_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0), pady=12)
        
        username_label = tk.Label(info_frame, text=self.user_info.get('username', 'User'),
                                 bg=self.colors['primary'], fg='white', font=self.fonts['subtitle'])
        username_label.pack(anchor=tk.W)
        
        status_label = tk.Label(info_frame, text="Online",
                               bg=self.colors['primary'], fg='lightgray', font=self.fonts['small'])
        status_label.pack(anchor=tk.W)
        
        # Control buttons
        controls_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        controls_frame.pack(side=tk.RIGHT, padx=15, pady=12)
        
        # Status button
        status_btn = tk.Button(controls_frame, text="📱", bg=self.colors['primary'], fg='white',
                              bd=0, font=self.fonts['emoji'], command=self.show_status_manager)
        status_btn.pack(side=tk.LEFT, padx=2)
        
        # New chat button
        new_chat_btn = tk.Button(controls_frame, text="💬", bg=self.colors['primary'], fg='white',
                                bd=0, font=self.fonts['emoji'], command=self.show_new_chat_dialog)
        new_chat_btn.pack(side=tk.LEFT, padx=2)
        
        # Menu button
        menu_btn = tk.Button(controls_frame, text="⋮", bg=self.colors['primary'], fg='white',
                            bd=0, font=self.fonts['subtitle'], command=self.show_main_menu)
        menu_btn.pack(side=tk.LEFT, padx=2)
    
    def create_navigation_tabs(self):
        """Create navigation tabs for different sections."""
        self.nav_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'], height=50)
        self.nav_frame.pack(fill=tk.X)
        self.nav_frame.pack_propagate(False)
        
        self.current_tab = "chats"
        
        # Tab buttons
        tabs = [
            ("💬", "chats", "Chats"),
            ("📱", "status", "Status"),
            ("📞", "calls", "Calls"),
            ("👥", "contacts", "Contacts")
        ]
        
        self.tab_buttons = {}
        for icon, tab_id, tooltip in tabs:
            btn = tk.Button(self.nav_frame, text=icon, bg=self.colors['sidebar'],
                           fg=self.colors['text_primary'], bd=0, font=self.fonts['emoji'],
                           command=lambda t=tab_id: self.switch_tab(t))
            btn.pack(side=tk.LEFT, fill=tk.X, expand=True, pady=10)
            self.tab_buttons[tab_id] = btn
        
        # Highlight current tab
        self.highlight_tab("chats")
    
    def create_search_bar(self):
        """Create search bar."""
        search_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'])
        search_frame.pack(fill=tk.X, padx=10, pady=10)
        
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=self.fonts['body'], bg='white', relief='solid', bd=1)
        search_entry.pack(fill=tk.X, ipady=8)
        search_entry.insert(0, "Search or start new chat")
        search_entry.bind('<FocusIn>', self.clear_search_placeholder)
        search_entry.bind('<KeyRelease>', self.on_search)
    
    def create_sidebar_content(self):
        """Create scrollable content area for sidebar."""
        # Content container
        self.content_container = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'])
        self.content_container.pack(fill=tk.BOTH, expand=True)
        
        # Scrollable content
        self.content_canvas = tk.Canvas(self.content_container, bg=self.colors['sidebar'], highlightthickness=0)
        self.content_scrollbar = ttk.Scrollbar(self.content_container, orient="vertical", command=self.content_canvas.yview)
        self.content_scrollable_frame = tk.Frame(self.content_canvas, bg=self.colors['sidebar'])
        
        self.content_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.content_canvas.configure(scrollregion=self.content_canvas.bbox("all"))
        )
        
        self.content_canvas.create_window((0, 0), window=self.content_scrollable_frame, anchor="nw")
        self.content_canvas.configure(yscrollcommand=self.content_scrollbar.set)
        
        self.content_canvas.pack(side="left", fill="both", expand=True)
        self.content_scrollbar.pack(side="right", fill="y")
        
        # Load initial content
        self.load_tab_content("chats")
    
    def create_chat_area(self):
        """Create main chat area."""
        # Chat area frame
        self.chat_frame = tk.Frame(self.main_frame, bg=self.colors['chat_bg'])
        self.chat_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Chat header
        self.create_chat_header()
        
        # Messages area
        self.create_messages_area()
        
        # Typing indicator
        self.create_typing_indicator()
        
        # Message input area
        self.create_message_input_area()
    
    def create_chat_header(self):
        """Create advanced chat header."""
        self.header_frame = tk.Frame(self.chat_frame, bg=self.colors['primary'], height=70)
        self.header_frame.pack(fill=tk.X)
        self.header_frame.pack_propagate(False)
        
        # Back button (mobile-like)
        back_btn = tk.Button(self.header_frame, text="←", bg=self.colors['primary'], fg='white',
                            bd=0, font=self.fonts['title'], command=self.go_back)
        back_btn.pack(side=tk.LEFT, padx=10, pady=20)
        
        # Chat avatar
        self.chat_avatar_frame = tk.Frame(self.header_frame, bg=self.colors['accent'], width=40, height=40)
        self.chat_avatar_frame.pack(side=tk.LEFT, padx=10, pady=15)
        self.chat_avatar_frame.pack_propagate(False)
        
        # Chat info
        chat_info_frame = tk.Frame(self.header_frame, bg=self.colors['primary'])
        chat_info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, pady=15)
        
        self.chat_name_label = tk.Label(chat_info_frame, text="Select a chat",
                                       bg=self.colors['primary'], fg='white', font=self.fonts['subtitle'])
        self.chat_name_label.pack(anchor=tk.W)
        
        self.chat_status_label = tk.Label(chat_info_frame, text="",
                                         bg=self.colors['primary'], fg='lightgray', font=self.fonts['small'])
        self.chat_status_label.pack(anchor=tk.W)
        
        # Action buttons
        actions_frame = tk.Frame(self.header_frame, bg=self.colors['primary'])
        actions_frame.pack(side=tk.RIGHT, padx=15, pady=15)
        
        # Video call button
        video_btn = tk.Button(actions_frame, text="📹", bg=self.colors['primary'], fg='white',
                             bd=0, font=self.fonts['emoji'], command=self.start_video_call)
        video_btn.pack(side=tk.LEFT, padx=2)
        
        # Voice call button
        voice_btn = tk.Button(actions_frame, text="📞", bg=self.colors['primary'], fg='white',
                             bd=0, font=self.fonts['emoji'], command=self.start_voice_call)
        voice_btn.pack(side=tk.LEFT, padx=2)
        
        # Search button
        search_btn = tk.Button(actions_frame, text="🔍", bg=self.colors['primary'], fg='white',
                              bd=0, font=self.fonts['emoji'], command=self.search_in_chat)
        search_btn.pack(side=tk.LEFT, padx=2)
        
        # Menu button
        chat_menu_btn = tk.Button(actions_frame, text="⋮", bg=self.colors['primary'], fg='white',
                                 bd=0, font=self.fonts['subtitle'], command=self.show_chat_menu)
        chat_menu_btn.pack(side=tk.LEFT, padx=2)
    
    def create_messages_area(self):
        """Create messages display area."""
        # Messages container with background pattern
        self.messages_container = tk.Frame(self.chat_frame, bg=self.colors['chat_bg'])
        self.messages_container.pack(fill=tk.BOTH, expand=True)
        
        # Scrollable messages
        self.messages_canvas = tk.Canvas(self.messages_container, bg=self.colors['chat_bg'], highlightthickness=0)
        self.messages_scrollbar = ttk.Scrollbar(self.messages_container, orient="vertical", command=self.messages_canvas.yview)
        self.messages_scrollable_frame = tk.Frame(self.messages_canvas, bg=self.colors['chat_bg'])
        
        self.messages_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.messages_canvas.configure(scrollregion=self.messages_canvas.bbox("all"))
        )
        
        self.messages_canvas.create_window((0, 0), window=self.messages_scrollable_frame, anchor="nw")
        self.messages_canvas.configure(yscrollcommand=self.messages_scrollbar.set)
        
        self.messages_canvas.pack(side="left", fill="both", expand=True)
        self.messages_scrollbar.pack(side="right", fill="y")
        
        # Welcome message
        self.show_welcome_message()
    
    def create_typing_indicator(self):
        """Create typing indicator."""
        self.typing_frame = tk.Frame(self.chat_frame, bg=self.colors['chat_bg'], height=30)
        self.typing_frame.pack(fill=tk.X)
        self.typing_frame.pack_propagate(False)
        
        self.typing_label = tk.Label(self.typing_frame, textvariable=self.typing_var,
                                    bg=self.colors['chat_bg'], fg=self.colors['typing'],
                                    font=self.fonts['small'])
        self.typing_label.pack(side=tk.LEFT, padx=20, pady=5)
    
    def create_message_input_area(self):
        """Create advanced message input area."""
        self.input_frame = tk.Frame(self.chat_frame, bg=self.colors['chat_bg'], height=80)
        self.input_frame.pack(fill=tk.X)
        self.input_frame.pack_propagate(False)
        
        # Input container
        input_container = tk.Frame(self.input_frame, bg='white', relief='solid', bd=1)
        input_container.pack(fill=tk.X, padx=10, pady=10)
        
        # Emoji button
        emoji_btn = tk.Button(input_container, text="😊", bg='white', fg=self.colors['text_secondary'],
                             bd=0, font=self.fonts['emoji'], command=self.show_emoji_picker)
        emoji_btn.pack(side=tk.LEFT, padx=5, pady=10)
        
        # Message entry
        self.message_entry = tk.Text(input_container, height=2, wrap=tk.WORD, bg='white',
                                    fg=self.colors['text_primary'], font=self.fonts['body'],
                                    relief='flat', bd=0)
        self.message_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=10)
        self.message_entry.bind('<KeyPress>', self.on_typing)
        self.message_entry.bind('<Return>', self.send_message)
        self.message_entry.bind('<Shift-Return>', lambda e: None)  # Allow line breaks with Shift+Enter
        
        # Attachment button
        attach_btn = tk.Button(input_container, text="📎", bg='white', fg=self.colors['text_secondary'],
                              bd=0, font=self.fonts['emoji'], command=self.show_attachment_menu)
        attach_btn.pack(side=tk.RIGHT, padx=5, pady=10)
        
        # Voice/Send button
        self.voice_send_btn = tk.Button(input_container, text="🎤", bg=self.colors['accent'], fg='white',
                                       bd=0, font=self.fonts['emoji'], relief='flat')
        self.voice_send_btn.pack(side=tk.RIGHT, padx=5, pady=10)
        self.voice_send_btn.bind('<Button-1>', self.start_voice_recording)
        self.voice_send_btn.bind('<ButtonRelease-1>', self.stop_voice_recording)
        
        # Bind text changes to switch between voice and send button
        self.message_entry.bind('<KeyRelease>', self.update_send_button)
    
    def create_info_panel(self):
        """Create info panel for contact/group details."""
        self.info_panel = tk.Frame(self.main_frame, bg=self.colors['sidebar'], width=300)
        # Initially hidden, shown when needed
        self.info_panel_visible = False

    # ==================== CORE FUNCTIONALITY ====================

    def load_all_data(self):
        """Load all data (chats, contacts, status, etc.)."""
        def load_thread():
            try:
                # Load chat rooms
                rooms = self.api_client.get_chat_rooms()
                if rooms:
                    self.chat_rooms = rooms

                # Load users
                users = self.api_client.get_users()
                if users:
                    self.users = [u for u in users if u['id'] != self.user_info['id']]

                # Load contacts
                contacts = self.api_client.get_contacts()
                if contacts:
                    self.contacts = contacts

                # Load status updates
                status_updates = self.api_client.get_status_updates()
                if status_updates:
                    self.status_updates = status_updates

                # Update UI
                self.parent.after(0, self.refresh_all_content)

            except Exception as e:
                print(f"Error loading data: {e}")

        threading.Thread(target=load_thread, daemon=True).start()

    def refresh_all_content(self):
        """Refresh all content in the UI."""
        self.load_tab_content(self.current_tab)

    def switch_tab(self, tab_id: str):
        """Switch between different tabs."""
        self.current_tab = tab_id
        self.highlight_tab(tab_id)
        self.load_tab_content(tab_id)

    def highlight_tab(self, tab_id: str):
        """Highlight the active tab."""
        for tid, btn in self.tab_buttons.items():
            if tid == tab_id:
                btn.config(bg=self.colors['accent'], fg='white')
            else:
                btn.config(bg=self.colors['sidebar'], fg=self.colors['text_primary'])

    def load_tab_content(self, tab_id: str):
        """Load content for the selected tab."""
        # Clear existing content
        for widget in self.content_scrollable_frame.winfo_children():
            widget.destroy()

        if tab_id == "chats":
            self.load_chats_content()
        elif tab_id == "status":
            self.load_status_content()
        elif tab_id == "calls":
            self.load_calls_content()
        elif tab_id == "contacts":
            self.load_contacts_content()

    def load_chats_content(self):
        """Load chats in the sidebar."""
        # Add chat rooms
        for room in self.chat_rooms:
            self.create_chat_item(room, is_room=True)

        # Add separator if both rooms and users exist
        if self.chat_rooms and self.users:
            separator = tk.Frame(self.content_scrollable_frame, bg=self.colors['border'], height=1)
            separator.pack(fill=tk.X, padx=20, pady=10)

        # Add users for private chats
        for user in self.users:
            self.create_chat_item(user, is_room=False)

    def load_status_content(self):
        """Load status updates in the sidebar."""
        # My status
        my_status_frame = tk.Frame(self.content_scrollable_frame, bg=self.colors['sidebar'])
        my_status_frame.pack(fill=tk.X, pady=10)

        my_status_btn = tk.Button(my_status_frame, text="📱 My Status",
                                 bg=self.colors['sidebar'], fg=self.colors['text_primary'],
                                 font=self.fonts['subtitle'], bd=0, anchor=tk.W,
                                 command=self.show_my_status)
        my_status_btn.pack(fill=tk.X, padx=20, pady=5)

        # Recent updates
        if self.status_updates:
            recent_label = tk.Label(self.content_scrollable_frame, text="Recent updates",
                                   bg=self.colors['sidebar'], fg=self.colors['text_secondary'],
                                   font=self.fonts['small'])
            recent_label.pack(anchor=tk.W, padx=20, pady=(10, 5))

            for status in self.status_updates:
                self.create_status_item(status)

    def load_calls_content(self):
        """Load call history in the sidebar."""
        # Call history header
        header_label = tk.Label(self.content_scrollable_frame, text="Recent calls",
                               bg=self.colors['sidebar'], fg=self.colors['text_secondary'],
                               font=self.fonts['small'])
        header_label.pack(anchor=tk.W, padx=20, pady=(10, 5))

        # Sample call history (would be loaded from API)
        call_history = [
            {"name": "Alice", "type": "video", "time": "2 hours ago", "incoming": True},
            {"name": "Bob", "type": "voice", "time": "Yesterday", "incoming": False},
            {"name": "Charlie", "type": "voice", "time": "2 days ago", "incoming": True}
        ]

        for call in call_history:
            self.create_call_item(call)

    def load_contacts_content(self):
        """Load contacts in the sidebar."""
        # Add contact button
        add_contact_btn = tk.Button(self.content_scrollable_frame, text="➕ Add Contact",
                                   bg=self.colors['sidebar'], fg=self.colors['accent'],
                                   font=self.fonts['subtitle'], bd=0, anchor=tk.W,
                                   command=self.add_new_contact)
        add_contact_btn.pack(fill=tk.X, padx=20, pady=10)

        # Contacts list
        for contact in self.contacts:
            self.create_contact_item(contact)

        # All users (if not in contacts)
        if self.users:
            all_users_label = tk.Label(self.content_scrollable_frame, text="All users",
                                      bg=self.colors['sidebar'], fg=self.colors['text_secondary'],
                                      font=self.fonts['small'])
            all_users_label.pack(anchor=tk.W, padx=20, pady=(20, 5))

            for user in self.users:
                if not any(c.get('user_id') == user['id'] for c in self.contacts):
                    self.create_contact_item(user, is_contact=False)

    def create_chat_item(self, item: Dict[str, Any], is_room: bool):
        """Create a chat list item."""
        item_frame = tk.Frame(self.content_scrollable_frame, bg=self.colors['sidebar'])
        item_frame.pack(fill=tk.X, pady=1)

        # Main button
        btn_frame = tk.Frame(item_frame, bg=self.colors['sidebar'])
        btn_frame.pack(fill=tk.X, padx=10, pady=5)

        # Avatar
        avatar_frame = tk.Frame(btn_frame, bg=self.colors['accent'], width=50, height=50)
        avatar_frame.pack(side=tk.LEFT, padx=(0, 15))
        avatar_frame.pack_propagate(False)

        # Avatar content
        if is_room:
            initial = item['name'][0].upper() if item['name'] else 'R'
            name = item['name']
            last_msg = f"Group • {item.get('member_count', 0)} members"
        else:
            initial = item['username'][0].upper()
            name = item['username']
            last_msg = "Online" if item.get('is_online', False) else "Last seen recently"

        avatar_canvas = tk.Canvas(avatar_frame, width=50, height=50, bg=self.colors['accent'], highlightthickness=0)
        avatar_canvas.pack()
        avatar_canvas.create_oval(5, 5, 45, 45, fill='white', outline=self.colors['primary'], width=2)
        avatar_canvas.create_text(25, 25, text=initial, fill=self.colors['primary'], font=self.fonts['subtitle'])

        # Info section
        info_frame = tk.Frame(btn_frame, bg=self.colors['sidebar'])
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Name and time row
        name_time_frame = tk.Frame(info_frame, bg=self.colors['sidebar'])
        name_time_frame.pack(fill=tk.X)

        name_label = tk.Label(name_time_frame, text=name, bg=self.colors['sidebar'],
                             fg=self.colors['text_primary'], font=self.fonts['subtitle'], anchor=tk.W)
        name_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        time_label = tk.Label(name_time_frame, text="now", bg=self.colors['sidebar'],
                             fg=self.colors['text_secondary'], font=self.fonts['small'])
        time_label.pack(side=tk.RIGHT)

        # Last message and status row
        msg_status_frame = tk.Frame(info_frame, bg=self.colors['sidebar'])
        msg_status_frame.pack(fill=tk.X, pady=(2, 0))

        msg_label = tk.Label(msg_status_frame, text=last_msg, bg=self.colors['sidebar'],
                            fg=self.colors['text_secondary'], font=self.fonts['small'], anchor=tk.W)
        msg_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Unread count (if any)
        if is_room and item.get('unread_count', 0) > 0:
            unread_frame = tk.Frame(msg_status_frame, bg=self.colors['accent'], width=20, height=20)
            unread_frame.pack(side=tk.RIGHT)
            unread_frame.pack_propagate(False)

            unread_label = tk.Label(unread_frame, text=str(item['unread_count']),
                                   bg=self.colors['accent'], fg='white', font=self.fonts['small'])
            unread_label.pack(expand=True)

        # Click handler
        def on_click(event=None):
            if is_room:
                self.select_room(item)
            else:
                self.start_private_chat(item)

        # Bind click events
        for widget in [item_frame, btn_frame, avatar_frame, avatar_canvas, info_frame,
                      name_time_frame, name_label, msg_status_frame, msg_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", lambda e: btn_frame.config(bg=self.colors['border']))
            widget.bind("<Leave>", lambda e: btn_frame.config(bg=self.colors['sidebar']))

    def create_status_item(self, status: Dict[str, Any]):
        """Create a status item."""
        item_frame = tk.Frame(self.content_scrollable_frame, bg=self.colors['sidebar'])
        item_frame.pack(fill=tk.X, pady=2)

        btn_frame = tk.Frame(item_frame, bg=self.colors['sidebar'])
        btn_frame.pack(fill=tk.X, padx=10, pady=5)

        # Status ring avatar
        avatar_frame = tk.Frame(btn_frame, width=50, height=50)
        avatar_frame.pack(side=tk.LEFT, padx=(0, 15))
        avatar_frame.pack_propagate(False)

        avatar_canvas = tk.Canvas(avatar_frame, width=50, height=50, bg=self.colors['sidebar'], highlightthickness=0)
        avatar_canvas.pack()

        # Draw status ring
        avatar_canvas.create_oval(2, 2, 48, 48, outline=self.colors['accent'], width=3)
        avatar_canvas.create_oval(8, 8, 42, 42, fill='white', outline=self.colors['primary'], width=2)

        initial = status.get('username', 'U')[0].upper()
        avatar_canvas.create_text(25, 25, text=initial, fill=self.colors['primary'], font=self.fonts['subtitle'])

        # Status info
        info_frame = tk.Frame(btn_frame, bg=self.colors['sidebar'])
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        name_label = tk.Label(info_frame, text=status.get('username', 'Unknown'),
                             bg=self.colors['sidebar'], fg=self.colors['text_primary'],
                             font=self.fonts['subtitle'], anchor=tk.W)
        name_label.pack(fill=tk.X)

        time_ago = self.get_time_ago(status.get('created_at', ''))
        time_label = tk.Label(info_frame, text=time_ago,
                             bg=self.colors['sidebar'], fg=self.colors['text_secondary'],
                             font=self.fonts['small'], anchor=tk.W)
        time_label.pack(fill=tk.X)

        # Click handler
        def on_click(event=None):
            self.view_status(status)

        for widget in [item_frame, btn_frame, avatar_frame, avatar_canvas, info_frame, name_label, time_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", lambda e: btn_frame.config(bg=self.colors['border']))
            widget.bind("<Leave>", lambda e: btn_frame.config(bg=self.colors['sidebar']))

    def create_call_item(self, call: Dict[str, Any]):
        """Create a call history item."""
        item_frame = tk.Frame(self.content_scrollable_frame, bg=self.colors['sidebar'])
        item_frame.pack(fill=tk.X, pady=2)

        btn_frame = tk.Frame(item_frame, bg=self.colors['sidebar'])
        btn_frame.pack(fill=tk.X, padx=10, pady=5)

        # Call type icon
        call_icon = "📹" if call['type'] == 'video' else "📞"
        if not call['incoming']:
            call_icon = "📤" + call_icon

        icon_label = tk.Label(btn_frame, text=call_icon, bg=self.colors['sidebar'],
                             fg=self.colors['accent'], font=self.fonts['emoji'])
        icon_label.pack(side=tk.LEFT, padx=(0, 15))

        # Call info
        info_frame = tk.Frame(btn_frame, bg=self.colors['sidebar'])
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        name_label = tk.Label(info_frame, text=call['name'], bg=self.colors['sidebar'],
                             fg=self.colors['text_primary'], font=self.fonts['subtitle'], anchor=tk.W)
        name_label.pack(fill=tk.X)

        time_label = tk.Label(info_frame, text=call['time'], bg=self.colors['sidebar'],
                             fg=self.colors['text_secondary'], font=self.fonts['small'], anchor=tk.W)
        time_label.pack(fill=tk.X)

        # Call back button
        callback_btn = tk.Button(btn_frame, text=call_icon, bg=self.colors['sidebar'],
                                fg=self.colors['accent'], bd=0, font=self.fonts['emoji'],
                                command=lambda: self.callback_user(call))
        callback_btn.pack(side=tk.RIGHT)

    def create_contact_item(self, contact: Dict[str, Any], is_contact: bool = True):
        """Create a contact item."""
        item_frame = tk.Frame(self.content_scrollable_frame, bg=self.colors['sidebar'])
        item_frame.pack(fill=tk.X, pady=2)

        btn_frame = tk.Frame(item_frame, bg=self.colors['sidebar'])
        btn_frame.pack(fill=tk.X, padx=10, pady=5)

        # Avatar
        avatar_frame = tk.Frame(btn_frame, bg=self.colors['accent'], width=40, height=40)
        avatar_frame.pack(side=tk.LEFT, padx=(0, 15))
        avatar_frame.pack_propagate(False)

        name = contact.get('contact_name') or contact.get('username', 'Unknown')
        initial = name[0].upper()

        avatar_canvas = tk.Canvas(avatar_frame, width=40, height=40, bg=self.colors['accent'], highlightthickness=0)
        avatar_canvas.pack()
        avatar_canvas.create_oval(3, 3, 37, 37, fill='white', outline=self.colors['primary'], width=2)
        avatar_canvas.create_text(20, 20, text=initial, fill=self.colors['primary'], font=self.fonts['body'])

        # Contact info
        info_frame = tk.Frame(btn_frame, bg=self.colors['sidebar'])
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        name_label = tk.Label(info_frame, text=name, bg=self.colors['sidebar'],
                             fg=self.colors['text_primary'], font=self.fonts['subtitle'], anchor=tk.W)
        name_label.pack(fill=tk.X)

        status_text = "Contact" if is_contact else "Available"
        status_label = tk.Label(info_frame, text=status_text, bg=self.colors['sidebar'],
                               fg=self.colors['text_secondary'], font=self.fonts['small'], anchor=tk.W)
        status_label.pack(fill=tk.X)

        # Action buttons
        actions_frame = tk.Frame(btn_frame, bg=self.colors['sidebar'])
        actions_frame.pack(side=tk.RIGHT)

        # Message button
        msg_btn = tk.Button(actions_frame, text="💬", bg=self.colors['sidebar'],
                           fg=self.colors['accent'], bd=0, font=self.fonts['emoji'],
                           command=lambda: self.start_private_chat(contact))
        msg_btn.pack(side=tk.LEFT, padx=2)

        # Call button
        call_btn = tk.Button(actions_frame, text="📞", bg=self.colors['sidebar'],
                            fg=self.colors['accent'], bd=0, font=self.fonts['emoji'],
                            command=lambda: self.start_voice_call_with_user(contact))
        call_btn.pack(side=tk.LEFT, padx=2)

    # ==================== MESSAGING FUNCTIONALITY ====================

    def select_room(self, room: Dict[str, Any]):
        """Select and join a chat room - FULLY FUNCTIONAL."""
        print(f"Selecting room: {room['name']}")

        self.current_room = room

        # Update chat header
        self.chat_name_label.config(text=room['name'])

        if room.get('is_group', False):
            status_text = f"Group • {room.get('member_count', 0)} members"
        else:
            status_text = "Online"

        self.chat_status_label.config(text=status_text)

        # Update chat avatar
        self.update_chat_avatar(room)

        # Clear welcome message and load messages
        for widget in self.messages_scrollable_frame.winfo_children():
            widget.destroy()

        self.load_messages(room['id'])

        # Join room via WebSocket
        if self.ws_client:
            self.ws_client.join_room(room['id'])

        print(f"✅ Joined room: {room['name']}")

    def start_private_chat(self, user: Dict[str, Any]):
        """Start private chat with user."""
        print(f"Starting private chat with: {user['username']}")

        # Create a temporary room object for private chat
        private_room = {
            'id': f"private_{user['id']}",
            'name': user['username'],
            'is_group': False,
            'member_count': 2,
            'other_user': user
        }

        self.select_room(private_room)

    def update_chat_avatar(self, room: Dict[str, Any]):
        """Update chat avatar in header."""
        # Clear existing avatar
        for widget in self.chat_avatar_frame.winfo_children():
            widget.destroy()

        # Create new avatar
        initial = room['name'][0].upper() if room['name'] else 'C'
        avatar_canvas = tk.Canvas(self.chat_avatar_frame, width=40, height=40,
                                 bg=self.colors['accent'], highlightthickness=0)
        avatar_canvas.pack()
        avatar_canvas.create_oval(3, 3, 37, 37, fill='white', outline=self.colors['primary'], width=2)
        avatar_canvas.create_text(20, 20, text=initial, fill=self.colors['primary'], font=self.fonts['body'])

    def load_messages(self, room_id: int):
        """Load messages for a room - FULLY FUNCTIONAL."""
        def load_thread():
            try:
                messages = self.api_client.get_messages(room_id)
                if messages:
                    self.messages = messages
                    self.parent.after(0, self.display_messages)
                else:
                    self.parent.after(0, self.show_no_messages)
            except Exception as e:
                print(f"Error loading messages: {e}")
                self.parent.after(0, self.show_no_messages)

        threading.Thread(target=load_thread, daemon=True).start()

    def display_messages(self):
        """Display messages in chat area."""
        for message in self.messages:
            self.add_message_bubble(message)

        # Scroll to bottom
        self.messages_canvas.update_idletasks()
        self.messages_canvas.yview_moveto(1.0)

    def show_no_messages(self):
        """Show no messages placeholder."""
        no_msg_frame = tk.Frame(self.messages_scrollable_frame, bg=self.colors['chat_bg'])
        no_msg_frame.pack(fill=tk.BOTH, expand=True)

        no_msg_label = tk.Label(no_msg_frame,
                               text="🔒 Messages are end-to-end encrypted.\nNo one outside of this chat can read them.",
                               bg=self.colors['chat_bg'], fg=self.colors['text_secondary'],
                               font=self.fonts['body'], justify=tk.CENTER)
        no_msg_label.pack(expand=True)

    def show_welcome_message(self):
        """Show welcome message when no chat is selected."""
        welcome_frame = tk.Frame(self.messages_scrollable_frame, bg=self.colors['chat_bg'])
        welcome_frame.pack(fill=tk.BOTH, expand=True)

        welcome_label = tk.Label(welcome_frame,
                                text="💬 WhatsApp-like Chat\n\nSelect a chat to start messaging\nwith end-to-end encryption.",
                                bg=self.colors['chat_bg'], fg=self.colors['text_secondary'],
                                font=self.fonts['body'], justify=tk.CENTER)
        welcome_label.pack(expand=True)

    def add_message_bubble(self, message: Dict[str, Any]):
        """Add advanced message bubble with all features."""
        is_own = message.get('sender_id') == self.user_info['id']

        # Message container
        msg_container = tk.Frame(self.messages_scrollable_frame, bg=self.colors['chat_bg'])
        msg_container.pack(fill=tk.X, pady=3, padx=15)

        # Store message ID for updates
        msg_container.message_id = message.get('id')

        # Message bubble frame
        if is_own:
            bubble_frame = tk.Frame(msg_container, bg=self.colors['chat_bg'])
            bubble_frame.pack(side=tk.RIGHT, anchor=tk.E)
            bubble_bg = self.colors['sent_msg']
            text_color = 'white' if self.dark_mode else self.colors['text_primary']
        else:
            bubble_frame = tk.Frame(msg_container, bg=self.colors['chat_bg'])
            bubble_frame.pack(side=tk.LEFT, anchor=tk.W)
            bubble_bg = self.colors['received_msg']
            text_color = self.colors['text_primary']

        # Bubble with rounded corners effect
        bubble = tk.Frame(bubble_frame, bg=bubble_bg, relief='solid', bd=0, padx=12, pady=8)
        bubble.pack()

        # Configure bubble shape
        if is_own:
            bubble.config(relief='solid', bd=1, highlightbackground=self.colors['sent_msg'])
        else:
            bubble.config(relief='solid', bd=1, highlightbackground=self.colors['received_msg'])

        # Sender name (for group chats)
        if not is_own and self.current_room and self.current_room.get('is_group', False):
            sender_label = tk.Label(bubble, text=message.get('sender_username', 'Unknown'),
                                   bg=bubble_bg, fg=self.colors['accent'], font=self.fonts['small'])
            sender_label.pack(anchor=tk.W)

        # Message content based on type
        message_type = message.get('message_type', 'text')

        if message_type == 'text':
            self.add_text_content(bubble, message, bubble_bg, text_color)
        elif message_type == 'voice':
            self.add_voice_content(bubble, message, bubble_bg, text_color)
        elif message_type == 'image':
            self.add_image_content(bubble, message, bubble_bg, text_color)
        elif message_type == 'document':
            self.add_document_content(bubble, message, bubble_bg, text_color)
        elif message_type == 'location':
            self.add_location_content(bubble, message, bubble_bg, text_color)

        # Bottom row with timestamp and status
        self.add_message_footer(bubble, message, bubble_bg, text_color, is_own)

        # Add context menu
        self.add_message_context_menu(bubble, message)

        # Auto-scroll to bottom
        self.messages_canvas.update_idletasks()
        self.messages_canvas.yview_moveto(1.0)

    def add_text_content(self, bubble, message, bubble_bg, text_color):
        """Add text content to message bubble."""
        content = message['content']

        # Check for mentions, links, etc.
        content_label = tk.Label(bubble, text=content, bg=bubble_bg, fg=text_color,
                                font=self.fonts['body'], wraplength=300, justify=tk.LEFT)
        content_label.pack(anchor=tk.W)

        # Make links clickable (basic implementation)
        if 'http' in content:
            content_label.config(fg='blue', cursor='hand2')
            content_label.bind('<Button-1>', lambda e: self.open_link(content))

    def add_voice_content(self, bubble, message, bubble_bg, text_color):
        """Add voice message content."""
        voice_frame = tk.Frame(bubble, bg=bubble_bg)
        voice_frame.pack(anchor=tk.W, fill=tk.X)

        # Play button
        play_btn = tk.Button(voice_frame, text="▶️", bg=bubble_bg, fg=text_color,
                            bd=0, font=self.fonts['emoji'],
                            command=lambda: self.play_voice_message(message))
        play_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Waveform visualization (simplified)
        wave_canvas = tk.Canvas(voice_frame, width=150, height=30, bg=bubble_bg, highlightthickness=0)
        wave_canvas.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Draw simple waveform
        for i in range(0, 150, 5):
            height = 5 + (i % 20)
            wave_canvas.create_line(i, 15-height//2, i, 15+height//2, fill=text_color, width=2)

        # Duration
        duration = message.get('duration', '0:30')
        duration_label = tk.Label(voice_frame, text=duration, bg=bubble_bg, fg=text_color,
                                 font=self.fonts['small'])
        duration_label.pack(side=tk.RIGHT, padx=(10, 0))

    def add_image_content(self, bubble, message, bubble_bg, text_color):
        """Add image content to message bubble."""
        img_frame = tk.Frame(bubble, bg=bubble_bg)
        img_frame.pack(anchor=tk.W)

        # Image placeholder (would load actual image)
        img_canvas = tk.Canvas(img_frame, width=200, height=150, bg='lightgray', highlightthickness=0)
        img_canvas.pack()
        img_canvas.create_text(100, 75, text="🖼️\nImage", fill='gray', font=self.fonts['body'], justify=tk.CENTER)

        # Image caption if any
        if message.get('caption'):
            caption_label = tk.Label(bubble, text=message['caption'], bg=bubble_bg, fg=text_color,
                                    font=self.fonts['body'], wraplength=200, justify=tk.LEFT)
            caption_label.pack(anchor=tk.W, pady=(5, 0))

    def add_document_content(self, bubble, message, bubble_bg, text_color):
        """Add document content to message bubble."""
        doc_frame = tk.Frame(bubble, bg=bubble_bg)
        doc_frame.pack(anchor=tk.W, fill=tk.X)

        # Document icon
        doc_icon = tk.Label(doc_frame, text="📄", bg=bubble_bg, fg=text_color,
                           font=self.fonts['emoji'])
        doc_icon.pack(side=tk.LEFT, padx=(0, 10))

        # Document info
        doc_info_frame = tk.Frame(doc_frame, bg=bubble_bg)
        doc_info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        filename = message.get('filename', 'Document')
        filename_label = tk.Label(doc_info_frame, text=filename, bg=bubble_bg, fg=text_color,
                                 font=self.fonts['body'], anchor=tk.W)
        filename_label.pack(fill=tk.X)

        filesize = message.get('filesize', '1.2 MB')
        size_label = tk.Label(doc_info_frame, text=filesize, bg=bubble_bg, fg=text_color,
                             font=self.fonts['small'], anchor=tk.W)
        size_label.pack(fill=tk.X)

        # Download button
        download_btn = tk.Button(doc_frame, text="⬇️", bg=bubble_bg, fg=text_color,
                                bd=0, font=self.fonts['emoji'],
                                command=lambda: self.download_file(message))
        download_btn.pack(side=tk.RIGHT)

    def add_location_content(self, bubble, message, bubble_bg, text_color):
        """Add location content to message bubble."""
        loc_frame = tk.Frame(bubble, bg=bubble_bg)
        loc_frame.pack(anchor=tk.W)

        # Map placeholder
        map_canvas = tk.Canvas(loc_frame, width=200, height=100, bg='lightblue', highlightthickness=0)
        map_canvas.pack()
        map_canvas.create_text(100, 50, text="📍\nLocation", fill='darkblue', font=self.fonts['body'], justify=tk.CENTER)

        # Location details
        location = message.get('location', 'Unknown Location')
        loc_label = tk.Label(bubble, text=location, bg=bubble_bg, fg=text_color,
                            font=self.fonts['body'], anchor=tk.W)
        loc_label.pack(anchor=tk.W, pady=(5, 0))

    def add_message_footer(self, bubble, message, bubble_bg, text_color, is_own):
        """Add message footer with timestamp and status."""
        footer_frame = tk.Frame(bubble, bg=bubble_bg)
        footer_frame.pack(fill=tk.X, pady=(5, 0))

        # Timestamp
        try:
            timestamp = format_timestamp(message['created_at'])
        except:
            timestamp = datetime.now().strftime("%H:%M")

        time_label = tk.Label(footer_frame, text=timestamp, bg=bubble_bg, fg=text_color,
                             font=self.fonts['small'])
        time_label.pack(side=tk.RIGHT)

        # Message status (for own messages)
        if is_own:
            status = message.get('status', 'sent')
            status_icon = self.get_status_icon(status)

            status_label = tk.Label(footer_frame, text=status_icon, bg=bubble_bg, fg=text_color,
                                   font=self.fonts['small'])
            status_label.pack(side=tk.RIGHT, padx=(0, 5))

    def get_status_icon(self, status: str) -> str:
        """Get status icon for message."""
        status_icons = {
            'sending': '🕐',
            'sent': '✓',
            'delivered': '✓✓',
            'read': '✓✓',
            'failed': '❌'
        }
        return status_icons.get(status, '✓')

    def send_message(self, event=None):
        """Send message - FULLY FUNCTIONAL with all types."""
        if not self.current_room:
            messagebox.showwarning("No Chat", "Please select a chat first!")
            return

        content = self.message_entry.get("1.0", tk.END).strip()
        if not content:
            return

        print(f"Sending message: {content}")

        # Clear input immediately
        self.message_entry.delete("1.0", tk.END)

        # Add message to UI immediately (optimistic update)
        temp_message = {
            'id': f"temp_{int(time.time() * 1000)}",
            'content': content,
            'sender_id': self.user_info['id'],
            'sender_username': self.user_info['username'],
            'chat_room_id': self.current_room['id'],
            'created_at': datetime.now().isoformat(),
            'message_type': 'text',
            'status': 'sending'
        }

        self.add_message_bubble(temp_message)

        # Send via API
        def send_thread():
            try:
                result = self.api_client.send_message(self.current_room['id'], content)
                if result:
                    print("✅ Message sent successfully")
                    # Update status
                    self.parent.after(0, lambda: self.update_message_status(temp_message['id'], 'sent'))

                    # Send via WebSocket for real-time delivery
                    if self.ws_client:
                        self.ws_client.send_message(self.current_room['id'], content)

                    # Show notification to other users
                    self.send_notification(content)
                else:
                    print("❌ Failed to send message")
                    self.parent.after(0, lambda: self.update_message_status(temp_message['id'], 'failed'))
            except Exception as e:
                print(f"❌ Error sending message: {e}")
                self.parent.after(0, lambda: self.update_message_status(temp_message['id'], 'failed'))

        threading.Thread(target=send_thread, daemon=True).start()

        # Update send button
        self.update_send_button()

        return "break"  # Prevent default Enter behavior

    # ==================== VOICE & VIDEO CALLING ====================

    def start_voice_call(self):
        """Start voice call - FULLY FUNCTIONAL."""
        if not self.current_room:
            messagebox.showwarning("No Chat", "Please select a chat first!")
            return

        self.initiate_call("voice")

    def start_video_call(self):
        """Start video call - FULLY FUNCTIONAL."""
        if not self.current_room:
            messagebox.showwarning("No Chat", "Please select a chat first!")
            return

        self.initiate_call("video")

    def start_voice_call_with_user(self, user: Dict[str, Any]):
        """Start voice call with specific user."""
        # Create temporary room for the call
        call_room = {
            'id': f"call_{user['id']}",
            'name': user['username'],
            'is_group': False,
            'other_user': user
        }
        self.current_room = call_room
        self.initiate_call("voice")

    def initiate_call(self, call_type: str):
        """Initiate a voice or video call."""
        print(f"Starting {call_type} call with {self.current_room['name']}")

        # Create call window
        self.call_window = tk.Toplevel(self.parent)
        self.call_window.title(f"{call_type.title()} Call - {self.current_room['name']}")
        self.call_window.geometry("400x500" if call_type == "video" else "300x200")
        self.call_window.transient(self.parent)
        self.call_window.configure(bg=self.colors['primary'])

        # Call interface
        self.create_call_interface(call_type)

        # Start call logic
        self.call_active = True
        self.call_type = call_type

        # Initialize audio/video
        if call_type == "video":
            self.start_video_capture()

        self.start_audio_call()

        # Send call notification via WebSocket
        if self.ws_client:
            self.ws_client.send_call_request(self.current_room['id'], call_type)

    def create_call_interface(self, call_type: str):
        """Create call interface."""
        # Header
        header_frame = tk.Frame(self.call_window, bg=self.colors['primary'])
        header_frame.pack(fill=tk.X, pady=20)

        # Contact name
        name_label = tk.Label(header_frame, text=self.current_room['name'],
                             bg=self.colors['primary'], fg='white',
                             font=self.fonts['title'])
        name_label.pack()

        # Call status
        self.call_status_label = tk.Label(header_frame, text="Calling...",
                                         bg=self.colors['primary'], fg='lightgray',
                                         font=self.fonts['body'])
        self.call_status_label.pack(pady=5)

        # Video area (if video call)
        if call_type == "video":
            self.video_frame = tk.Frame(self.call_window, bg='black', height=300)
            self.video_frame.pack(fill=tk.X, padx=20, pady=10)
            self.video_frame.pack_propagate(False)

            # Video canvas
            self.video_canvas = tk.Canvas(self.video_frame, bg='black', highlightthickness=0)
            self.video_canvas.pack(fill=tk.BOTH, expand=True)

            # Self video (small window)
            self.self_video_frame = tk.Frame(self.video_frame, bg='gray', width=100, height=80)
            self.self_video_frame.place(x=10, y=10)
            self.self_video_frame.pack_propagate(False)

        # Call controls
        controls_frame = tk.Frame(self.call_window, bg=self.colors['primary'])
        controls_frame.pack(side=tk.BOTTOM, pady=20)

        # Mute button
        self.mute_btn = tk.Button(controls_frame, text="🔇", bg='gray', fg='white',
                                 font=self.fonts['emoji'], bd=0, width=3, height=2,
                                 command=self.toggle_mute)
        self.mute_btn.pack(side=tk.LEFT, padx=10)

        # End call button
        end_call_btn = tk.Button(controls_frame, text="📞", bg='red', fg='white',
                                font=self.fonts['emoji'], bd=0, width=3, height=2,
                                command=self.end_call)
        end_call_btn.pack(side=tk.LEFT, padx=10)

        # Speaker button
        self.speaker_btn = tk.Button(controls_frame, text="🔊", bg='gray', fg='white',
                                    font=self.fonts['emoji'], bd=0, width=3, height=2,
                                    command=self.toggle_speaker)
        self.speaker_btn.pack(side=tk.LEFT, padx=10)

        # Video toggle (if video call)
        if call_type == "video":
            self.video_btn = tk.Button(controls_frame, text="📹", bg='gray', fg='white',
                                      font=self.fonts['emoji'], bd=0, width=3, height=2,
                                      command=self.toggle_video)
            self.video_btn.pack(side=tk.LEFT, padx=10)

    def start_video_capture(self):
        """Start video capture for video calls."""
        try:
            self.video_capture = cv2.VideoCapture(0)
            if self.video_capture.isOpened():
                self.update_video_feed()
                print("✅ Video capture started")
            else:
                print("❌ Could not open video capture")
        except Exception as e:
            print(f"❌ Video capture error: {e}")

    def update_video_feed(self):
        """Update video feed in call window."""
        if self.video_capture and self.video_capture.isOpened() and hasattr(self, 'video_canvas'):
            ret, frame = self.video_capture.read()
            if ret:
                # Convert frame to display format
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frame = cv2.resize(frame, (360, 240))

                # Convert to PhotoImage
                image = Image.fromarray(frame)
                photo = ImageTk.PhotoImage(image)

                # Update canvas
                self.video_canvas.delete("all")
                self.video_canvas.create_image(180, 120, image=photo)
                self.video_canvas.image = photo  # Keep a reference

            # Schedule next update
            if self.call_active:
                self.call_window.after(33, self.update_video_feed)  # ~30 FPS

    def start_audio_call(self):
        """Start audio for calls."""
        if not self.audio_initialized:
            print("❌ Audio not initialized")
            return

        try:
            # Audio call simulation
            self.call_status_label.config(text="Connected")
            print("✅ Audio call started")
        except Exception as e:
            print(f"❌ Audio call error: {e}")

    def toggle_mute(self):
        """Toggle mute during call."""
        if hasattr(self, 'muted'):
            self.muted = not self.muted
        else:
            self.muted = True

        if self.muted:
            self.mute_btn.config(bg='red', text="🔇")
            print("🔇 Muted")
        else:
            self.mute_btn.config(bg='gray', text="🎤")
            print("🎤 Unmuted")

    def toggle_speaker(self):
        """Toggle speaker during call."""
        if hasattr(self, 'speaker_on'):
            self.speaker_on = not self.speaker_on
        else:
            self.speaker_on = True

        if self.speaker_on:
            self.speaker_btn.config(bg=self.colors['accent'], text="🔊")
            print("🔊 Speaker on")
        else:
            self.speaker_btn.config(bg='gray', text="🔈")
            print("🔈 Speaker off")

    def toggle_video(self):
        """Toggle video during video call."""
        if hasattr(self, 'video_on'):
            self.video_on = not self.video_on
        else:
            self.video_on = True

        if self.video_on:
            self.video_btn.config(bg=self.colors['accent'], text="📹")
            if self.video_capture:
                self.update_video_feed()
            print("📹 Video on")
        else:
            self.video_btn.config(bg='gray', text="📷")
            if hasattr(self, 'video_canvas'):
                self.video_canvas.delete("all")
                self.video_canvas.create_text(180, 120, text="Video Off", fill='white', font=self.fonts['body'])
            print("📷 Video off")

    def end_call(self):
        """End the current call."""
        print("📞 Ending call")

        self.call_active = False

        # Stop video capture
        if hasattr(self, 'video_capture') and self.video_capture:
            self.video_capture.release()

        # Close call window
        if hasattr(self, 'call_window'):
            self.call_window.destroy()

        # Send end call notification
        if self.ws_client:
            self.ws_client.send_call_end(self.current_room['id'])

        print("✅ Call ended")

    def callback_user(self, call_info: Dict[str, Any]):
        """Call back a user from call history."""
        # Find user and start call
        user = next((u for u in self.users if u['username'] == call_info['name']), None)
        if user:
            if call_info['type'] == 'video':
                self.start_voice_call_with_user(user)
            else:
                self.start_voice_call_with_user(user)

    # ==================== VOICE MESSAGES ====================

    def start_voice_recording(self, event=None):
        """Start recording voice message."""
        if not self.audio_initialized:
            messagebox.showerror("Audio Error", "Audio system not available")
            return

        print("🎤 Starting voice recording...")
        self.is_recording = True
        self.audio_frames = []

        # Update button
        self.voice_send_btn.config(text="🔴", bg='red')

        # Start recording thread
        threading.Thread(target=self.record_audio, daemon=True).start()

    def stop_voice_recording(self, event=None):
        """Stop recording and send voice message."""
        if not self.is_recording:
            return

        print("🎤 Stopping voice recording...")
        self.is_recording = False

        # Update button
        self.voice_send_btn.config(text="🎤", bg=self.colors['accent'])

        # Process and send voice message
        self.process_voice_message()

    def record_audio(self):
        """Record audio in background thread."""
        try:
            # Audio recording parameters
            chunk = 1024
            format = pyaudio.paInt16
            channels = 1
            rate = 44100

            # Start recording
            stream = self.audio.open(format=format, channels=channels,
                                   rate=rate, input=True, frames_per_buffer=chunk)

            while self.is_recording:
                data = stream.read(chunk)
                self.audio_frames.append(data)

            # Stop recording
            stream.stop_stream()
            stream.close()

            print("✅ Voice recording completed")

        except Exception as e:
            print(f"❌ Voice recording error: {e}")
            self.is_recording = False

    def process_voice_message(self):
        """Process and send voice message."""
        if not self.audio_frames:
            return

        try:
            # Save audio to temporary file
            temp_filename = f"voice_{int(time.time())}.wav"
            temp_path = os.path.join("temp", temp_filename)

            # Create temp directory if it doesn't exist
            os.makedirs("temp", exist_ok=True)

            # Save WAV file
            wf = wave.open(temp_path, 'wb')
            wf.setnchannels(1)
            wf.setsampwidth(self.audio.get_sample_size(pyaudio.paInt16))
            wf.setframerate(44100)
            wf.writeframes(b''.join(self.audio_frames))
            wf.close()

            # Calculate duration
            duration = len(self.audio_frames) * 1024 / 44100
            duration_str = f"{int(duration//60)}:{int(duration%60):02d}"

            # Send voice message
            self.send_voice_message(temp_path, duration_str)

        except Exception as e:
            print(f"❌ Voice processing error: {e}")

    def send_voice_message(self, file_path: str, duration: str):
        """Send voice message."""
        if not self.current_room:
            return

        # Add voice message to UI
        voice_message = {
            'id': f"voice_{int(time.time() * 1000)}",
            'content': f"🎤 Voice message ({duration})",
            'sender_id': self.user_info['id'],
            'sender_username': self.user_info['username'],
            'chat_room_id': self.current_room['id'],
            'created_at': datetime.now().isoformat(),
            'message_type': 'voice',
            'duration': duration,
            'file_path': file_path,
            'status': 'sending'
        }

        self.add_message_bubble(voice_message)

        # Send via API (would upload file)
        def send_voice_thread():
            try:
                # Simulate file upload and message sending
                time.sleep(1)  # Simulate upload time
                print(f"✅ Voice message sent: {duration}")

                # Update status
                self.parent.after(0, lambda: self.update_message_status(voice_message['id'], 'sent'))

                # Send notification via WebSocket
                if self.ws_client:
                    self.ws_client.send_voice_message(self.current_room['id'], file_path, duration)

            except Exception as e:
                print(f"❌ Voice message send error: {e}")
                self.parent.after(0, lambda: self.update_message_status(voice_message['id'], 'failed'))

        threading.Thread(target=send_voice_thread, daemon=True).start()

    def play_voice_message(self, message: Dict[str, Any]):
        """Play voice message."""
        try:
            file_path = message.get('file_path')
            if file_path and os.path.exists(file_path):
                pygame.mixer.music.load(file_path)
                pygame.mixer.music.play()
                print(f"🔊 Playing voice message: {message.get('duration', 'unknown')}")
            else:
                print("❌ Voice file not found")
        except Exception as e:
            print(f"❌ Voice playback error: {e}")

    # ==================== FILE SHARING & ATTACHMENTS ====================

    def show_attachment_menu(self):
        """Show attachment options menu."""
        menu = tk.Menu(self.parent, tearoff=0)
        menu.add_command(label="📷 Camera", command=self.take_photo)
        menu.add_command(label="🖼️ Photo & Video", command=self.send_media)
        menu.add_command(label="📄 Document", command=self.send_document)
        menu.add_command(label="🎵 Audio", command=self.send_audio_file)
        menu.add_command(label="📍 Location", command=self.send_location)
        menu.add_command(label="👤 Contact", command=self.send_contact)

        try:
            menu.tk_popup(self.parent.winfo_pointerx(), self.parent.winfo_pointery())
        finally:
            menu.grab_release()

    def take_photo(self):
        """Take photo with camera."""
        try:
            # Initialize camera
            cap = cv2.VideoCapture(0)
            if not cap.isOpened():
                messagebox.showerror("Camera Error", "Could not access camera")
                return

            # Create camera window
            camera_window = tk.Toplevel(self.parent)
            camera_window.title("Take Photo")
            camera_window.geometry("640x480")

            # Camera canvas
            camera_canvas = tk.Canvas(camera_window, width=640, height=480, bg='black')
            camera_canvas.pack()

            # Controls
            controls_frame = tk.Frame(camera_window)
            controls_frame.pack(pady=10)

            def update_camera():
                ret, frame = cap.read()
                if ret:
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    frame = cv2.resize(frame, (640, 480))
                    image = Image.fromarray(frame)
                    photo = ImageTk.PhotoImage(image)
                    camera_canvas.delete("all")
                    camera_canvas.create_image(320, 240, image=photo)
                    camera_canvas.image = photo

                if camera_window.winfo_exists():
                    camera_window.after(33, update_camera)

            def capture_photo():
                ret, frame = cap.read()
                if ret:
                    # Save photo
                    filename = f"photo_{int(time.time())}.jpg"
                    filepath = os.path.join("temp", filename)
                    os.makedirs("temp", exist_ok=True)
                    cv2.imwrite(filepath, frame)

                    # Send photo
                    self.send_image_message(filepath)

                cap.release()
                camera_window.destroy()

            def cancel_photo():
                cap.release()
                camera_window.destroy()

            # Buttons
            tk.Button(controls_frame, text="📷 Capture", command=capture_photo,
                     bg=self.colors['accent'], fg='white', font=self.fonts['body']).pack(side=tk.LEFT, padx=10)
            tk.Button(controls_frame, text="❌ Cancel", command=cancel_photo,
                     bg='red', fg='white', font=self.fonts['body']).pack(side=tk.LEFT, padx=10)

            # Start camera
            update_camera()

        except Exception as e:
            messagebox.showerror("Camera Error", f"Camera error: {e}")

    def send_media(self):
        """Send photo or video from gallery."""
        file_path = filedialog.askopenfilename(
            title="Select Media",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("Video files", "*.mp4 *.avi *.mov *.mkv"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            if file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                self.send_image_message(file_path)
            else:
                self.send_video_message(file_path)

    def send_document(self):
        """Send document file."""
        file_path = filedialog.askopenfilename(
            title="Select Document",
            filetypes=[
                ("PDF files", "*.pdf"),
                ("Word documents", "*.doc *.docx"),
                ("Excel files", "*.xls *.xlsx"),
                ("PowerPoint", "*.ppt *.pptx"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.send_document_message(file_path)

    def send_audio_file(self):
        """Send audio file."""
        file_path = filedialog.askopenfilename(
            title="Select Audio",
            filetypes=[
                ("Audio files", "*.mp3 *.wav *.ogg *.m4a *.flac"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.send_audio_message(file_path)

    def send_location(self):
        """Send current location."""
        # Location sharing dialog
        location_window = tk.Toplevel(self.parent)
        location_window.title("Share Location")
        location_window.geometry("400x300")
        location_window.transient(self.parent)

        # Location info
        info_frame = tk.Frame(location_window, padding="20")
        info_frame.pack(fill=tk.BOTH, expand=True)

        tk.Label(info_frame, text="Share Location", font=self.fonts['title']).pack(pady=10)

        # Mock location (in real app, would use GPS)
        location_data = {
            'latitude': 40.7128,
            'longitude': -74.0060,
            'address': "New York, NY, USA"
        }

        tk.Label(info_frame, text=f"📍 {location_data['address']}",
                font=self.fonts['body']).pack(pady=10)

        # Buttons
        btn_frame = tk.Frame(info_frame)
        btn_frame.pack(pady=20)

        def send_loc():
            self.send_location_message(location_data)
            location_window.destroy()

        tk.Button(btn_frame, text="Send Location", command=send_loc,
                 bg=self.colors['accent'], fg='white', font=self.fonts['body']).pack(side=tk.LEFT, padx=10)
        tk.Button(btn_frame, text="Cancel", command=location_window.destroy,
                 bg='gray', fg='white', font=self.fonts['body']).pack(side=tk.LEFT, padx=10)

    def send_contact(self):
        """Send contact information."""
        # Contact selection dialog
        contact_window = tk.Toplevel(self.parent)
        contact_window.title("Share Contact")
        contact_window.geometry("300x400")
        contact_window.transient(self.parent)

        # Contact list
        contacts_frame = tk.Frame(contact_window)
        contacts_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        tk.Label(contacts_frame, text="Select Contact to Share", font=self.fonts['subtitle']).pack(pady=10)

        # List of contacts
        for contact in self.contacts[:5]:  # Show first 5 contacts
            contact_btn = tk.Button(contacts_frame,
                                   text=f"👤 {contact.get('contact_name', contact.get('username', 'Unknown'))}",
                                   bg=self.colors['sidebar'], fg=self.colors['text_primary'],
                                   font=self.fonts['body'], anchor=tk.W,
                                   command=lambda c=contact: self.send_contact_message(c, contact_window))
            contact_btn.pack(fill=tk.X, pady=2)

    def send_image_message(self, file_path: str):
        """Send image message."""
        if not self.current_room:
            return

        filename = os.path.basename(file_path)
        filesize = os.path.getsize(file_path)
        filesize_str = self.format_file_size(filesize)

        # Add image message to UI
        image_message = {
            'id': f"img_{int(time.time() * 1000)}",
            'content': f"📷 {filename}",
            'sender_id': self.user_info['id'],
            'sender_username': self.user_info['username'],
            'chat_room_id': self.current_room['id'],
            'created_at': datetime.now().isoformat(),
            'message_type': 'image',
            'filename': filename,
            'filesize': filesize_str,
            'file_path': file_path,
            'status': 'sending'
        }

        self.add_message_bubble(image_message)

        # Send via API
        def send_image_thread():
            try:
                # Simulate file upload
                time.sleep(2)
                print(f"✅ Image sent: {filename}")
                self.parent.after(0, lambda: self.update_message_status(image_message['id'], 'sent'))
            except Exception as e:
                print(f"❌ Image send error: {e}")
                self.parent.after(0, lambda: self.update_message_status(image_message['id'], 'failed'))

        threading.Thread(target=send_image_thread, daemon=True).start()

    def send_document_message(self, file_path: str):
        """Send document message."""
        if not self.current_room:
            return

        filename = os.path.basename(file_path)
        filesize = os.path.getsize(file_path)
        filesize_str = self.format_file_size(filesize)

        # Add document message to UI
        doc_message = {
            'id': f"doc_{int(time.time() * 1000)}",
            'content': f"📄 {filename}",
            'sender_id': self.user_info['id'],
            'sender_username': self.user_info['username'],
            'chat_room_id': self.current_room['id'],
            'created_at': datetime.now().isoformat(),
            'message_type': 'document',
            'filename': filename,
            'filesize': filesize_str,
            'file_path': file_path,
            'status': 'sending'
        }

        self.add_message_bubble(doc_message)

        # Send via API
        def send_doc_thread():
            try:
                time.sleep(1)
                print(f"✅ Document sent: {filename}")
                self.parent.after(0, lambda: self.update_message_status(doc_message['id'], 'sent'))
            except Exception as e:
                print(f"❌ Document send error: {e}")
                self.parent.after(0, lambda: self.update_message_status(doc_message['id'], 'failed'))

        threading.Thread(target=send_doc_thread, daemon=True).start()

    def send_location_message(self, location_data: Dict[str, Any]):
        """Send location message."""
        if not self.current_room:
            return

        # Add location message to UI
        loc_message = {
            'id': f"loc_{int(time.time() * 1000)}",
            'content': f"📍 {location_data['address']}",
            'sender_id': self.user_info['id'],
            'sender_username': self.user_info['username'],
            'chat_room_id': self.current_room['id'],
            'created_at': datetime.now().isoformat(),
            'message_type': 'location',
            'location': location_data['address'],
            'latitude': location_data['latitude'],
            'longitude': location_data['longitude'],
            'status': 'sending'
        }

        self.add_message_bubble(loc_message)

        # Send via API
        def send_loc_thread():
            try:
                time.sleep(1)
                print(f"✅ Location sent: {location_data['address']}")
                self.parent.after(0, lambda: self.update_message_status(loc_message['id'], 'sent'))
            except Exception as e:
                print(f"❌ Location send error: {e}")
                self.parent.after(0, lambda: self.update_message_status(loc_message['id'], 'failed'))

        threading.Thread(target=send_loc_thread, daemon=True).start()

    def send_contact_message(self, contact: Dict[str, Any], window):
        """Send contact message."""
        if not self.current_room:
            return

        contact_name = contact.get('contact_name', contact.get('username', 'Unknown'))

        # Add contact message to UI
        contact_message = {
            'id': f"contact_{int(time.time() * 1000)}",
            'content': f"👤 {contact_name}",
            'sender_id': self.user_info['id'],
            'sender_username': self.user_info['username'],
            'chat_room_id': self.current_room['id'],
            'created_at': datetime.now().isoformat(),
            'message_type': 'contact',
            'contact_name': contact_name,
            'contact_info': contact,
            'status': 'sending'
        }

        self.add_message_bubble(contact_message)
        window.destroy()

        # Send via API
        def send_contact_thread():
            try:
                time.sleep(1)
                print(f"✅ Contact sent: {contact_name}")
                self.parent.after(0, lambda: self.update_message_status(contact_message['id'], 'sent'))
            except Exception as e:
                print(f"❌ Contact send error: {e}")
                self.parent.after(0, lambda: self.update_message_status(contact_message['id'], 'failed'))

        threading.Thread(target=send_contact_thread, daemon=True).start()

    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format."""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def download_file(self, message: Dict[str, Any]):
        """Download file from message."""
        filename = message.get('filename', 'file')
        save_path = filedialog.asksaveasfilename(
            title="Save File",
            defaultextension=os.path.splitext(filename)[1],
            initialvalue=filename
        )

        if save_path:
            # Simulate file download
            def download_thread():
                try:
                    time.sleep(2)  # Simulate download time
                    print(f"✅ File downloaded: {filename}")
                    self.parent.after(0, lambda: messagebox.showinfo("Download", f"File saved to {save_path}"))
                except Exception as e:
                    print(f"❌ Download error: {e}")
                    self.parent.after(0, lambda: messagebox.showerror("Download Error", f"Failed to download {filename}"))

            threading.Thread(target=download_thread, daemon=True).start()

    # ==================== STATUS UPDATES ====================

    def show_status_manager(self):
        """Show status manager window."""
        status_window = tk.Toplevel(self.parent)
        status_window.title("Status Updates")
        status_window.geometry("400x500")
        status_window.transient(self.parent)

        # Header
        header_frame = tk.Frame(status_window, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="Status Updates", bg=self.colors['primary'], fg='white',
                font=self.fonts['title']).pack(expand=True)

        # My status section
        my_status_frame = tk.Frame(status_window, bg=self.colors['sidebar'])
        my_status_frame.pack(fill=tk.X, padx=20, pady=20)

        tk.Label(my_status_frame, text="My Status", bg=self.colors['sidebar'],
                fg=self.colors['text_primary'], font=self.fonts['subtitle']).pack(anchor=tk.W)

        # Add status button
        add_status_btn = tk.Button(my_status_frame, text="➕ Add Status Update",
                                  bg=self.colors['accent'], fg='white', font=self.fonts['body'],
                                  command=self.create_status_update)
        add_status_btn.pack(fill=tk.X, pady=10)

        # Recent status updates
        recent_frame = tk.Frame(status_window, bg=self.colors['sidebar'])
        recent_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        tk.Label(recent_frame, text="Recent Updates", bg=self.colors['sidebar'],
                fg=self.colors['text_primary'], font=self.fonts['subtitle']).pack(anchor=tk.W, pady=(0, 10))

        # Status list
        for status in self.status_updates[:5]:  # Show recent 5
            self.create_status_preview(recent_frame, status)

    def create_status_update(self):
        """Create new status update."""
        create_window = tk.Toplevel(self.parent)
        create_window.title("Create Status")
        create_window.geometry("400x300")
        create_window.transient(self.parent)

        # Content frame
        content_frame = tk.Frame(create_window, padding="20")
        content_frame.pack(fill=tk.BOTH, expand=True)

        tk.Label(content_frame, text="Create Status Update", font=self.fonts['title']).pack(pady=10)

        # Status type selection
        type_frame = tk.Frame(content_frame)
        type_frame.pack(fill=tk.X, pady=10)

        status_type = tk.StringVar(value="text")

        tk.Radiobutton(type_frame, text="📝 Text", variable=status_type, value="text",
                      font=self.fonts['body']).pack(side=tk.LEFT, padx=10)
        tk.Radiobutton(type_frame, text="📷 Photo", variable=status_type, value="photo",
                      font=self.fonts['body']).pack(side=tk.LEFT, padx=10)
        tk.Radiobutton(type_frame, text="🎥 Video", variable=status_type, value="video",
                      font=self.fonts['body']).pack(side=tk.LEFT, padx=10)

        # Content input
        tk.Label(content_frame, text="Status Content:", font=self.fonts['body']).pack(anchor=tk.W, pady=(10, 5))

        content_text = tk.Text(content_frame, height=5, wrap=tk.WORD, font=self.fonts['body'])
        content_text.pack(fill=tk.X, pady=5)
        content_text.insert("1.0", "What's on your mind?")

        # Buttons
        btn_frame = tk.Frame(content_frame)
        btn_frame.pack(fill=tk.X, pady=20)

        def post_status():
            content = content_text.get("1.0", tk.END).strip()
            if content and content != "What's on your mind?":
                self.post_status_update(status_type.get(), content)
                create_window.destroy()

        tk.Button(btn_frame, text="Post Status", command=post_status,
                 bg=self.colors['accent'], fg='white', font=self.fonts['body']).pack(side=tk.LEFT, padx=10)
        tk.Button(btn_frame, text="Cancel", command=create_window.destroy,
                 bg='gray', fg='white', font=self.fonts['body']).pack(side=tk.LEFT, padx=10)

    def post_status_update(self, status_type: str, content: str):
        """Post status update."""
        status_data = {
            'id': f"status_{int(time.time() * 1000)}",
            'user_id': self.user_info['id'],
            'username': self.user_info['username'],
            'type': status_type,
            'content': content,
            'created_at': datetime.now().isoformat(),
            'expires_at': (datetime.now() + timedelta(hours=24)).isoformat()
        }

        # Add to local status list
        self.status_updates.insert(0, status_data)

        # Send via API
        def post_status_thread():
            try:
                # Simulate API call
                time.sleep(1)
                print(f"✅ Status posted: {content[:50]}...")

                # Send notification
                self.send_notification(f"New status from {self.user_info['username']}")

            except Exception as e:
                print(f"❌ Status post error: {e}")

        threading.Thread(target=post_status_thread, daemon=True).start()

        messagebox.showinfo("Status Posted", "Your status has been posted!")

    def create_status_preview(self, parent, status: Dict[str, Any]):
        """Create status preview item."""
        preview_frame = tk.Frame(parent, bg=self.colors['sidebar'])
        preview_frame.pack(fill=tk.X, pady=5)

        # Status ring
        ring_frame = tk.Frame(preview_frame, width=50, height=50)
        ring_frame.pack(side=tk.LEFT, padx=(0, 15))
        ring_frame.pack_propagate(False)

        ring_canvas = tk.Canvas(ring_frame, width=50, height=50, bg=self.colors['sidebar'], highlightthickness=0)
        ring_canvas.pack()

        # Draw status ring
        ring_canvas.create_oval(2, 2, 48, 48, outline=self.colors['accent'], width=3)
        ring_canvas.create_oval(8, 8, 42, 42, fill='white', outline=self.colors['primary'], width=2)

        initial = status.get('username', 'U')[0].upper()
        ring_canvas.create_text(25, 25, text=initial, fill=self.colors['primary'], font=self.fonts['body'])

        # Status info
        info_frame = tk.Frame(preview_frame, bg=self.colors['sidebar'])
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        name_label = tk.Label(info_frame, text=status.get('username', 'Unknown'),
                             bg=self.colors['sidebar'], fg=self.colors['text_primary'],
                             font=self.fonts['subtitle'], anchor=tk.W)
        name_label.pack(fill=tk.X)

        time_ago = self.get_time_ago(status.get('created_at', ''))
        time_label = tk.Label(info_frame, text=time_ago,
                             bg=self.colors['sidebar'], fg=self.colors['text_secondary'],
                             font=self.fonts['small'], anchor=tk.W)
        time_label.pack(fill=tk.X)

        # Click to view
        def view_status(event=None):
            self.view_status(status)

        for widget in [preview_frame, ring_frame, ring_canvas, info_frame, name_label, time_label]:
            widget.bind("<Button-1>", view_status)

    def view_status(self, status: Dict[str, Any]):
        """View full status update."""
        view_window = tk.Toplevel(self.parent)
        view_window.title(f"Status - {status.get('username', 'Unknown')}")
        view_window.geometry("400x600")
        view_window.transient(self.parent)
        view_window.configure(bg='black')

        # Status content
        content_frame = tk.Frame(view_window, bg='black')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # User info
        user_frame = tk.Frame(content_frame, bg='black')
        user_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(user_frame, text=status.get('username', 'Unknown'), bg='black', fg='white',
                font=self.fonts['title']).pack(side=tk.LEFT)

        time_ago = self.get_time_ago(status.get('created_at', ''))
        tk.Label(user_frame, text=time_ago, bg='black', fg='gray',
                font=self.fonts['small']).pack(side=tk.RIGHT)

        # Status content
        if status.get('type') == 'text':
            tk.Label(content_frame, text=status.get('content', ''), bg='black', fg='white',
                    font=self.fonts['body'], wraplength=350, justify=tk.CENTER).pack(expand=True)
        else:
            # Placeholder for media content
            tk.Label(content_frame, text=f"📷 {status.get('type', 'media').title()} Status",
                    bg='black', fg='white', font=self.fonts['title']).pack(expand=True)

        # Close button
        tk.Button(content_frame, text="Close", command=view_window.destroy,
                 bg=self.colors['accent'], fg='white', font=self.fonts['body']).pack(pady=20)

    def show_my_status(self):
        """Show my status updates."""
        my_statuses = [s for s in self.status_updates if s.get('user_id') == self.user_info['id']]

        if not my_statuses:
            self.create_status_update()
        else:
            self.view_status(my_statuses[0])

    def get_time_ago(self, timestamp: str) -> str:
        """Get human readable time ago."""
        try:
            created_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            now = datetime.now()
            diff = now - created_time

            if diff.days > 0:
                return f"{diff.days}d ago"
            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f"{hours}h ago"
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f"{minutes}m ago"
            else:
                return "Just now"
        except:
            return "Recently"

    # ==================== EMOJI & UI HELPERS ====================

    def show_emoji_picker(self):
        """Show emoji picker window."""
        emoji_window = tk.Toplevel(self.parent)
        emoji_window.title("Emojis")
        emoji_window.geometry("400x300")
        emoji_window.transient(self.parent)

        # Emoji categories
        categories = {
            "😊 Smileys": ["😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇", "🙂", "🙃", "😉", "😌", "😍", "🥰"],
            "❤️ Hearts": ["❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔", "❣️", "💕", "💞", "💓", "💗", "💖"],
            "👍 Gestures": ["👍", "👎", "👌", "🤌", "🤏", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉", "👆", "🖕", "👇", "☝️"],
            "🎉 Objects": ["🎉", "🎊", "🎈", "🎁", "🎀", "🎂", "🍰", "🧁", "🍭", "🍬", "🍫", "🍩", "🍪", "☕", "🍺", "🍷"]
        }

        # Create notebook for categories
        notebook = ttk.Notebook(emoji_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        for category, emojis in categories.items():
            frame = tk.Frame(notebook)
            notebook.add(frame, text=category.split()[1])

            # Create grid of emoji buttons
            for i, emoji in enumerate(emojis):
                row = i // 8
                col = i % 8

                emoji_btn = tk.Button(frame, text=emoji, font=self.fonts['emoji'],
                                     bd=0, bg='white', command=lambda e=emoji: self.insert_emoji(e, emoji_window))
                emoji_btn.grid(row=row, column=col, padx=2, pady=2, sticky='nsew')

            # Configure grid weights
            for i in range(8):
                frame.columnconfigure(i, weight=1)

    def insert_emoji(self, emoji: str, window):
        """Insert emoji into message input."""
        current_text = self.message_entry.get("1.0", tk.END)
        cursor_pos = self.message_entry.index(tk.INSERT)

        # Insert emoji at cursor position
        self.message_entry.insert(cursor_pos, emoji)
        window.destroy()
        self.message_entry.focus_set()

        # Update send button
        self.update_send_button()

    def update_send_button(self, event=None):
        """Update send button based on message content."""
        content = self.message_entry.get("1.0", tk.END).strip()

        if content:
            self.voice_send_btn.config(text="➤", command=self.send_message)
        else:
            self.voice_send_btn.config(text="🎤")
            self.voice_send_btn.bind('<Button-1>', self.start_voice_recording)
            self.voice_send_btn.bind('<ButtonRelease-1>', self.stop_voice_recording)

    def clear_search_placeholder(self, event=None):
        """Clear search placeholder text."""
        if self.search_var.get() == "Search or start new chat":
            self.search_var.set("")

    def on_search(self, event=None):
        """Handle search input."""
        search_term = self.search_var.get().lower()
        if search_term and search_term != "search or start new chat":
            self.filter_content(search_term)
        else:
            self.load_tab_content(self.current_tab)

    def filter_content(self, search_term: str):
        """Filter content based on search term."""
        # Clear existing content
        for widget in self.content_scrollable_frame.winfo_children():
            widget.destroy()

        if self.current_tab == "chats":
            # Filter chats
            for room in self.chat_rooms:
                if search_term in room['name'].lower():
                    self.create_chat_item(room, is_room=True)

            for user in self.users:
                if search_term in user['username'].lower():
                    self.create_chat_item(user, is_room=False)

        elif self.current_tab == "contacts":
            # Filter contacts
            for contact in self.contacts:
                name = contact.get('contact_name', contact.get('username', '')).lower()
                if search_term in name:
                    self.create_contact_item(contact)

    def on_typing(self, event=None):
        """Handle typing indicator."""
        if self.current_room and self.ws_client:
            self.ws_client.start_typing(self.current_room['id'])

        # Update send button
        self.update_send_button()

    def update_message_status(self, message_id: str, status: str):
        """Update message status in UI."""
        # Find and update the message bubble
        for widget in self.messages_scrollable_frame.winfo_children():
            if hasattr(widget, 'message_id') and widget.message_id == message_id:
                # Update status indicator if it exists
                for child in widget.winfo_children():
                    if hasattr(child, 'status_label'):
                        if status == 'sent':
                            child.status_label.config(text="✓")
                        elif status == 'delivered':
                            child.status_label.config(text="✓✓")
                        elif status == 'read':
                            child.status_label.config(text="✓✓", fg='blue')
                        elif status == 'failed':
                            child.status_label.config(text="❌", fg='red')
                break

    # ==================== MENU ACTIONS & DIALOGS ====================

    def show_main_menu(self):
        """Show main application menu."""
        menu = tk.Menu(self.parent, tearoff=0)
        menu.add_command(label="👤 Profile", command=self.show_profile)
        menu.add_command(label="⚙️ Settings", command=self.show_settings)
        menu.add_command(label="📱 Status", command=self.show_status_manager)
        menu.add_separator()
        menu.add_command(label="🌙 Dark Mode", command=self.toggle_dark_mode)
        menu.add_command(label="🔔 Notifications", command=self.show_notification_settings)
        menu.add_separator()
        menu.add_command(label="ℹ️ About", command=self.show_about)
        menu.add_command(label="🚪 Logout", command=self.on_logout)

        try:
            menu.tk_popup(self.parent.winfo_pointerx(), self.parent.winfo_pointery())
        finally:
            menu.grab_release()

    def show_chat_menu(self):
        """Show chat-specific menu."""
        if not self.current_room:
            return

        menu = tk.Menu(self.parent, tearoff=0)
        menu.add_command(label="ℹ️ Chat Info", command=self.show_chat_info)
        menu.add_command(label="🔍 Search Messages", command=self.search_in_chat)
        menu.add_command(label="📎 Shared Media", command=self.show_shared_media)
        menu.add_separator()
        menu.add_command(label="🔇 Mute Chat", command=self.mute_chat)
        menu.add_command(label="📌 Pin Chat", command=self.pin_chat)
        menu.add_separator()

        if self.current_room.get('is_group', False):
            menu.add_command(label="👥 Group Settings", command=self.show_group_settings)
            menu.add_command(label="🚪 Leave Group", command=self.leave_group)
        else:
            menu.add_command(label="🚫 Block Contact", command=self.block_contact)

        try:
            menu.tk_popup(self.parent.winfo_pointerx(), self.parent.winfo_pointery())
        finally:
            menu.grab_release()

    def show_new_chat_dialog(self):
        """Show new chat creation dialog."""
        dialog = tk.Toplevel(self.parent)
        dialog.title("New Chat")
        dialog.geometry("350x400")
        dialog.transient(self.parent)

        # Header
        header_frame = tk.Frame(dialog, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        tk.Label(header_frame, text="Start New Chat", bg=self.colors['primary'], fg='white',
                font=self.fonts['title']).pack(expand=True)

        # Options
        options_frame = tk.Frame(dialog, padding="20")
        options_frame.pack(fill=tk.BOTH, expand=True)

        # New group
        group_btn = tk.Button(options_frame, text="👥 New Group",
                             bg=self.colors['accent'], fg='white', font=self.fonts['body'],
                             command=lambda: self.create_new_group(dialog))
        group_btn.pack(fill=tk.X, pady=5)

        # New broadcast
        broadcast_btn = tk.Button(options_frame, text="📢 New Broadcast",
                                 bg=self.colors['secondary'], fg='white', font=self.fonts['body'],
                                 command=lambda: self.create_new_broadcast(dialog))
        broadcast_btn.pack(fill=tk.X, pady=5)

        # Separator
        tk.Label(options_frame, text="Select Contact", font=self.fonts['subtitle']).pack(pady=(20, 10))

        # Contact list
        contacts_frame = tk.Frame(options_frame)
        contacts_frame.pack(fill=tk.BOTH, expand=True)

        # Scrollable contact list
        canvas = tk.Canvas(contacts_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(contacts_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Add contacts
        for user in self.users:
            contact_btn = tk.Button(scrollable_frame,
                                   text=f"👤 {user['username']}",
                                   bg='white', fg=self.colors['text_primary'],
                                   font=self.fonts['body'], anchor=tk.W,
                                   command=lambda u=user: self.start_chat_with_user(u, dialog))
            contact_btn.pack(fill=tk.X, pady=1)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_new_group(self, parent_dialog):
        """Create new group chat."""
        parent_dialog.destroy()

        group_dialog = tk.Toplevel(self.parent)
        group_dialog.title("New Group")
        group_dialog.geometry("400x500")
        group_dialog.transient(self.parent)

        # Group creation form
        form_frame = tk.Frame(group_dialog, padding="20")
        form_frame.pack(fill=tk.BOTH, expand=True)

        tk.Label(form_frame, text="Create New Group", font=self.fonts['title']).pack(pady=10)

        # Group name
        tk.Label(form_frame, text="Group Name:", font=self.fonts['body']).pack(anchor=tk.W, pady=(10, 5))
        group_name_entry = tk.Entry(form_frame, font=self.fonts['body'])
        group_name_entry.pack(fill=tk.X, pady=5)

        # Group description
        tk.Label(form_frame, text="Description (optional):", font=self.fonts['body']).pack(anchor=tk.W, pady=(10, 5))
        desc_text = tk.Text(form_frame, height=3, font=self.fonts['body'])
        desc_text.pack(fill=tk.X, pady=5)

        # Member selection
        tk.Label(form_frame, text="Add Members:", font=self.fonts['body']).pack(anchor=tk.W, pady=(10, 5))

        # Selected members
        selected_members = []
        selected_frame = tk.Frame(form_frame)
        selected_frame.pack(fill=tk.X, pady=5)

        def update_selected_display():
            for widget in selected_frame.winfo_children():
                widget.destroy()

            for member in selected_members:
                member_label = tk.Label(selected_frame, text=f"👤 {member['username']}",
                                       bg=self.colors['accent'], fg='white', font=self.fonts['small'])
                member_label.pack(side=tk.LEFT, padx=2, pady=2)

        # Available members
        members_frame = tk.Frame(form_frame)
        members_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        for user in self.users:
            def toggle_member(u=user):
                if u in selected_members:
                    selected_members.remove(u)
                else:
                    selected_members.append(u)
                update_selected_display()

            member_btn = tk.Button(members_frame, text=f"👤 {user['username']}",
                                  bg='white', fg=self.colors['text_primary'],
                                  font=self.fonts['body'], anchor=tk.W,
                                  command=toggle_member)
            member_btn.pack(fill=tk.X, pady=1)

        # Create button
        def create_group():
            name = group_name_entry.get().strip()
            description = desc_text.get("1.0", tk.END).strip()

            if name and selected_members:
                self.create_group_chat(name, description, selected_members)
                group_dialog.destroy()
            else:
                messagebox.showwarning("Invalid Input", "Please enter group name and select members")

        tk.Button(form_frame, text="Create Group", command=create_group,
                 bg=self.colors['accent'], fg='white', font=self.fonts['body']).pack(pady=20)

    def create_group_chat(self, name: str, description: str, members: List[Dict[str, Any]]):
        """Create a new group chat."""
        group_data = {
            'name': name,
            'description': description,
            'members': [m['id'] for m in members],
            'admin': self.user_info['id']
        }

        def create_group_thread():
            try:
                # Simulate API call
                time.sleep(1)

                # Add to local chat rooms
                new_group = {
                    'id': f"group_{int(time.time())}",
                    'name': name,
                    'description': description,
                    'is_group': True,
                    'member_count': len(members) + 1,
                    'admin_id': self.user_info['id']
                }

                self.chat_rooms.insert(0, new_group)
                self.parent.after(0, lambda: self.refresh_all_content())

                print(f"✅ Group created: {name}")
                self.parent.after(0, lambda: messagebox.showinfo("Group Created", f"Group '{name}' created successfully!"))

            except Exception as e:
                print(f"❌ Group creation error: {e}")
                self.parent.after(0, lambda: messagebox.showerror("Error", "Failed to create group"))

        threading.Thread(target=create_group_thread, daemon=True).start()

    def start_chat_with_user(self, user: Dict[str, Any], dialog):
        """Start chat with selected user."""
        dialog.destroy()
        self.start_private_chat(user)

    def add_new_contact(self):
        """Add new contact dialog."""
        contact_dialog = tk.Toplevel(self.parent)
        contact_dialog.title("Add Contact")
        contact_dialog.geometry("300x200")
        contact_dialog.transient(self.parent)

        form_frame = tk.Frame(contact_dialog, padding="20")
        form_frame.pack(fill=tk.BOTH, expand=True)

        tk.Label(form_frame, text="Add New Contact", font=self.fonts['title']).pack(pady=10)

        tk.Label(form_frame, text="Username:", font=self.fonts['body']).pack(anchor=tk.W, pady=(10, 5))
        username_entry = tk.Entry(form_frame, font=self.fonts['body'])
        username_entry.pack(fill=tk.X, pady=5)

        def add_contact():
            username = username_entry.get().strip()
            if username:
                # Find user and add to contacts
                user = next((u for u in self.users if u['username'] == username), None)
                if user:
                    contact_data = {
                        'user_id': user['id'],
                        'contact_name': user['username'],
                        'username': user['username']
                    }
                    self.contacts.append(contact_data)
                    contact_dialog.destroy()
                    self.refresh_all_content()
                    messagebox.showinfo("Contact Added", f"Added {username} to contacts")
                else:
                    messagebox.showerror("User Not Found", f"User '{username}' not found")

        tk.Button(form_frame, text="Add Contact", command=add_contact,
                 bg=self.colors['accent'], fg='white', font=self.fonts['body']).pack(pady=20)

    # ==================== WEBSOCKET & NOTIFICATIONS ====================

    def setup_websocket(self):
        """Setup WebSocket connection for real-time features."""
        token = self.token_manager.get_access_token()
        if token:
            self.ws_client = WebSocketClient(token, self.handle_websocket_message)
            self.ws_client.start()
            print("✅ WebSocket connected")

    def handle_websocket_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket messages."""
        try:
            message_type = data.get('type')

            if message_type == 'message':
                self.handle_incoming_message(data.get('data', {}))
            elif message_type == 'typing':
                self.handle_typing_indicator(data.get('data', {}))
            elif message_type == 'call_request':
                self.handle_incoming_call(data.get('data', {}))
            elif message_type == 'user_online':
                self.handle_user_online(data.get('data', {}))
            elif message_type == 'user_offline':
                self.handle_user_offline(data.get('data', {}))

        except Exception as e:
            print(f"WebSocket message error: {e}")

    def handle_incoming_message(self, message_data: Dict[str, Any]):
        """Handle incoming message."""
        if (self.current_room and
            message_data.get('chat_room_id') == self.current_room['id'] and
            message_data.get('sender_id') != self.user_info['id']):

            self.parent.after(0, lambda: self.add_message_bubble(message_data))

            # Show notification
            sender = message_data.get('sender_username', 'Someone')
            content = message_data.get('content', 'New message')
            self.send_notification(f"{sender}: {content[:50]}...")

    def handle_typing_indicator(self, data: Dict[str, Any]):
        """Handle typing indicator."""
        if (self.current_room and
            data.get('chat_room_id') == self.current_room['id'] and
            data.get('user_id') != self.user_info['id']):

            username = data.get('username', 'Someone')
            self.parent.after(0, lambda: self.typing_var.set(f"{username} is typing..."))

            # Clear typing indicator after 3 seconds
            self.parent.after(3000, lambda: self.typing_var.set(""))

    def handle_incoming_call(self, call_data: Dict[str, Any]):
        """Handle incoming call request."""
        caller = call_data.get('caller_username', 'Someone')
        call_type = call_data.get('call_type', 'voice')

        # Show incoming call dialog
        self.parent.after(0, lambda: self.show_incoming_call_dialog(caller, call_type, call_data))

    def show_incoming_call_dialog(self, caller: str, call_type: str, call_data: Dict[str, Any]):
        """Show incoming call dialog."""
        call_dialog = tk.Toplevel(self.parent)
        call_dialog.title("Incoming Call")
        call_dialog.geometry("300x200")
        call_dialog.transient(self.parent)
        call_dialog.configure(bg=self.colors['primary'])

        # Call info
        info_frame = tk.Frame(call_dialog, bg=self.colors['primary'])
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        tk.Label(info_frame, text=f"Incoming {call_type} call", bg=self.colors['primary'], fg='white',
                font=self.fonts['subtitle']).pack()

        tk.Label(info_frame, text=caller, bg=self.colors['primary'], fg='white',
                font=self.fonts['title']).pack(pady=10)

        # Call buttons
        btn_frame = tk.Frame(info_frame, bg=self.colors['primary'])
        btn_frame.pack(side=tk.BOTTOM, pady=20)

        # Accept button
        accept_btn = tk.Button(btn_frame, text="📞", bg='green', fg='white',
                              font=self.fonts['emoji'], bd=0, width=4, height=2,
                              command=lambda: self.accept_call(call_data, call_dialog))
        accept_btn.pack(side=tk.LEFT, padx=10)

        # Decline button
        decline_btn = tk.Button(btn_frame, text="📞", bg='red', fg='white',
                               font=self.fonts['emoji'], bd=0, width=4, height=2,
                               command=lambda: self.decline_call(call_data, call_dialog))
        decline_btn.pack(side=tk.LEFT, padx=10)

    def accept_call(self, call_data: Dict[str, Any], dialog):
        """Accept incoming call."""
        dialog.destroy()

        # Set up call room
        caller_id = call_data.get('caller_id')
        caller_username = call_data.get('caller_username')

        call_room = {
            'id': f"call_{caller_id}",
            'name': caller_username,
            'is_group': False
        }

        self.current_room = call_room
        self.initiate_call(call_data.get('call_type', 'voice'))

    def decline_call(self, call_data: Dict[str, Any], dialog):
        """Decline incoming call."""
        dialog.destroy()

        # Send decline notification
        if self.ws_client:
            self.ws_client.send_call_decline(call_data.get('caller_id'))

    def setup_notifications(self):
        """Setup notification system."""
        try:
            # Test notification
            notification.notify(
                title="Chat App",
                message="Notifications enabled",
                timeout=2
            )
            self.notifications_enabled = True
            print("✅ Notifications enabled")
        except Exception as e:
            print(f"❌ Notifications not available: {e}")
            self.notifications_enabled = False

    def send_notification(self, message: str):
        """Send desktop notification."""
        if hasattr(self, 'notifications_enabled') and self.notifications_enabled:
            try:
                notification.notify(
                    title="WhatsApp-like Chat",
                    message=message,
                    timeout=5
                )
            except Exception as e:
                print(f"Notification error: {e}")

    # ==================== UTILITY METHODS ====================

    def go_back(self):
        """Go back to chat list."""
        self.current_room = None
        self.chat_name_label.config(text="Select a chat")
        self.chat_status_label.config(text="")

        # Clear messages
        for widget in self.messages_scrollable_frame.winfo_children():
            widget.destroy()

        self.show_welcome_message()

    def open_link(self, url: str):
        """Open link in browser."""
        import webbrowser
        webbrowser.open(url)

    def cleanup(self):
        """Clean up resources."""
        if self.ws_client:
            self.ws_client.stop()

        if hasattr(self, 'video_capture') and self.video_capture:
            self.video_capture.release()

        if hasattr(self, 'audio') and self.audio:
            self.audio.terminate()

    def destroy(self):
        """Destroy the window and clean up."""
        self.cleanup()
        for widget in self.parent.winfo_children():
            widget.destroy()

    # Placeholder methods for menu actions
    def show_profile(self): messagebox.showinfo("Profile", "Profile settings")
    def show_settings(self): messagebox.showinfo("Settings", "Application settings")
    def show_notification_settings(self): messagebox.showinfo("Notifications", "Notification settings")
    def show_about(self): messagebox.showinfo("About", "Advanced WhatsApp-like Chat v1.0")
    def toggle_dark_mode(self):
        self.dark_mode = not self.dark_mode
        self.setup_theme()
        messagebox.showinfo("Theme", f"{'Dark' if self.dark_mode else 'Light'} mode enabled")
    def show_chat_info(self): messagebox.showinfo("Chat Info", f"Info for {self.current_room['name']}")
    def search_in_chat(self): messagebox.showinfo("Search", "Search in chat")
    def show_shared_media(self): messagebox.showinfo("Media", "Shared media")
    def mute_chat(self): messagebox.showinfo("Mute", "Chat muted")
    def pin_chat(self): messagebox.showinfo("Pin", "Chat pinned")
    def show_group_settings(self): messagebox.showinfo("Group", "Group settings")
    def leave_group(self): messagebox.showinfo("Leave", "Left group")
    def block_contact(self): messagebox.showinfo("Block", "Contact blocked")
    def create_new_broadcast(self, dialog):
        dialog.destroy()
        messagebox.showinfo("Broadcast", "Broadcast feature")
    def handle_user_online(self, data): pass
    def handle_user_offline(self, data): pass
    def add_message_context_menu(self, widget, message): pass  # Already implemented above
