import tkinter as tk
from tkinter import ttk, font
from PIL import Image, ImageTk
import emoji
from datetime import datetime
from typing import Dict, Any, Optional, Callable
import os

class MessageBubble:
    """Modern WhatsApp-like message bubble component."""
    
    def __init__(self, parent, message_data: Dict[str, Any], is_own_message: bool, 
                 colors: Dict[str, str], fonts: Dict[str, font.Font],
                 on_reaction_click: Optional[Callable] = None,
                 on_reply_click: Optional[Callable] = None):
        self.parent = parent
        self.message_data = message_data
        self.is_own_message = is_own_message
        self.colors = colors
        self.fonts = fonts
        self.on_reaction_click = on_reaction_click
        self.on_reply_click = on_reply_click
        
        self.bubble_frame = None
        self.create_bubble()
    
    def create_bubble(self):
        """Create the message bubble."""
        # Main container for the message
        container = tk.Frame(self.parent, bg=self.colors['chat_bg'])
        container.pack(fill=tk.X, padx=10, pady=2)
        
        # Message alignment
        if self.is_own_message:
            bubble_container = tk.Frame(container, bg=self.colors['chat_bg'])
            bubble_container.pack(side=tk.RIGHT, anchor=tk.E)
            bubble_bg = self.colors['sent_msg']
        else:
            bubble_container = tk.Frame(container, bg=self.colors['chat_bg'])
            bubble_container.pack(side=tk.LEFT, anchor=tk.W)
            bubble_bg = self.colors['received_msg']
        
        # Create bubble frame with rounded appearance
        self.bubble_frame = tk.Frame(bubble_container, bg=bubble_bg, 
                                    relief='solid', bd=1, padx=12, pady=8)
        self.bubble_frame.pack(anchor=tk.E if self.is_own_message else tk.W)
        
        # Add reply indicator if this is a reply
        if self.message_data.get('reply_to_id'):
            self.add_reply_indicator()
        
        # Add message content based on type
        message_type = self.message_data.get('message_type', 'text')
        
        if message_type == 'text':
            self.add_text_content()
        elif message_type == 'image':
            self.add_image_content()
        elif message_type == 'voice':
            self.add_voice_content()
        elif message_type == 'file':
            self.add_file_content()
        elif message_type == 'location':
            self.add_location_content()
        elif message_type == 'contact':
            self.add_contact_content()
        else:
            self.add_text_content()  # Fallback
        
        # Add message metadata (time, status, reactions)
        self.add_message_metadata()
        
        # Add reactions if any
        if self.message_data.get('reactions'):
            self.add_reactions()
        
        # Add context menu
        self.add_context_menu()
    
    def add_reply_indicator(self):
        """Add reply indicator at the top of the message."""
        reply_frame = tk.Frame(self.bubble_frame, 
                              bg=self.colors['border'] if self.is_own_message else self.colors['accent'],
                              height=3)
        reply_frame.pack(fill=tk.X, pady=(0, 5))
        
        reply_text = self.message_data.get('reply_to_content', 'Original message')
        if len(reply_text) > 50:
            reply_text = reply_text[:50] + "..."
        
        reply_label = tk.Label(self.bubble_frame,
                              text=f"↳ {reply_text}",
                              bg=self.bubble_frame['bg'],
                              fg=self.colors['text_secondary'],
                              font=self.fonts['small'],
                              wraplength=250,
                              justify=tk.LEFT)
        reply_label.pack(anchor=tk.W, pady=(0, 5))
    
    def add_text_content(self):
        """Add text message content."""
        content = self.message_data.get('content', '')
        
        # Process emojis
        content = emoji.emojize(content, language='alias')
        
        # Create text label
        text_label = tk.Label(self.bubble_frame,
                             text=content,
                             bg=self.bubble_frame['bg'],
                             fg=self.colors['text_primary'],
                             font=self.fonts['body'],
                             wraplength=300,
                             justify=tk.LEFT)
        text_label.pack(anchor=tk.W)
    
    def add_image_content(self):
        """Add image message content."""
        # Image placeholder or actual image
        image_frame = tk.Frame(self.bubble_frame, bg=self.bubble_frame['bg'])
        image_frame.pack(pady=5)
        
        # Try to load and display image
        image_url = self.message_data.get('file_url')
        if image_url and os.path.exists(image_url):
            try:
                # Load and resize image
                img = Image.open(image_url)
                img.thumbnail((250, 250), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(img)
                
                image_label = tk.Label(image_frame, image=photo, bg=self.bubble_frame['bg'])
                image_label.image = photo  # Keep a reference
                image_label.pack()
                
                # Add click handler for full view
                image_label.bind("<Button-1>", lambda e: self.view_full_image(image_url))
                
            except Exception as e:
                # Fallback to placeholder
                placeholder_label = tk.Label(image_frame,
                                            text="🖼️ Image",
                                            bg=self.bubble_frame['bg'],
                                            fg=self.colors['text_secondary'],
                                            font=self.fonts['emoji'])
                placeholder_label.pack()
        else:
            # Image placeholder
            placeholder_label = tk.Label(image_frame,
                                        text="🖼️ Image",
                                        bg=self.bubble_frame['bg'],
                                        fg=self.colors['text_secondary'],
                                        font=self.fonts['emoji'])
            placeholder_label.pack()
        
        # Add caption if any
        caption = self.message_data.get('content', '').strip()
        if caption:
            caption_label = tk.Label(self.bubble_frame,
                                   text=caption,
                                   bg=self.bubble_frame['bg'],
                                   fg=self.colors['text_primary'],
                                   font=self.fonts['body'],
                                   wraplength=250,
                                   justify=tk.LEFT)
            caption_label.pack(anchor=tk.W, pady=(5, 0))
    
    def add_voice_content(self):
        """Add voice message content."""
        voice_frame = tk.Frame(self.bubble_frame, bg=self.bubble_frame['bg'])
        voice_frame.pack(pady=5)
        
        # Voice message controls
        controls_frame = tk.Frame(voice_frame, bg=self.bubble_frame['bg'])
        controls_frame.pack(fill=tk.X)
        
        # Play button
        play_button = tk.Button(controls_frame,
                               text="▶️",
                               bg=self.bubble_frame['bg'],
                               fg=self.colors['accent'],
                               bd=0,
                               font=self.fonts['emoji'],
                               command=lambda: self.play_voice_message())
        play_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Duration
        duration = self.message_data.get('duration', 0)
        duration_text = f"{int(duration//60):02d}:{int(duration%60):02d}"
        
        duration_label = tk.Label(controls_frame,
                                 text=duration_text,
                                 bg=self.bubble_frame['bg'],
                                 fg=self.colors['text_secondary'],
                                 font=self.fonts['small'])
        duration_label.pack(side=tk.LEFT)
        
        # Waveform visualization (simplified)
        waveform_canvas = tk.Canvas(controls_frame, width=100, height=20,
                                   bg=self.bubble_frame['bg'], highlightthickness=0)
        waveform_canvas.pack(side=tk.LEFT, padx=(10, 0))
        
        # Draw simple waveform
        for i in range(0, 100, 5):
            height = (i % 20) + 5
            waveform_canvas.create_line(i, 20-height, i, 20, fill=self.colors['accent'], width=2)
    
    def add_file_content(self):
        """Add file message content."""
        file_frame = tk.Frame(self.bubble_frame, bg=self.bubble_frame['bg'])
        file_frame.pack(pady=5)
        
        # File icon and info
        file_info_frame = tk.Frame(file_frame, bg=self.bubble_frame['bg'])
        file_info_frame.pack(fill=tk.X)
        
        # File icon
        file_icon = tk.Label(file_info_frame,
                            text="📄",
                            bg=self.bubble_frame['bg'],
                            font=self.fonts['emoji'])
        file_icon.pack(side=tk.LEFT, padx=(0, 10))
        
        # File details
        file_name = self.message_data.get('file_name', 'Unknown file')
        file_size = self.message_data.get('file_size', 0)
        
        # Format file size
        if file_size > 1024*1024:
            size_text = f"{file_size/(1024*1024):.1f} MB"
        elif file_size > 1024:
            size_text = f"{file_size/1024:.1f} KB"
        else:
            size_text = f"{file_size} B"
        
        file_details_frame = tk.Frame(file_info_frame, bg=self.bubble_frame['bg'])
        file_details_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        file_name_label = tk.Label(file_details_frame,
                                  text=file_name,
                                  bg=self.bubble_frame['bg'],
                                  fg=self.colors['text_primary'],
                                  font=self.fonts['body'],
                                  anchor=tk.W)
        file_name_label.pack(fill=tk.X)
        
        file_size_label = tk.Label(file_details_frame,
                                  text=size_text,
                                  bg=self.bubble_frame['bg'],
                                  fg=self.colors['text_secondary'],
                                  font=self.fonts['small'],
                                  anchor=tk.W)
        file_size_label.pack(fill=tk.X)
        
        # Download button
        download_button = tk.Button(file_info_frame,
                                   text="⬇️",
                                   bg=self.bubble_frame['bg'],
                                   fg=self.colors['accent'],
                                   bd=0,
                                   font=self.fonts['emoji'],
                                   command=lambda: self.download_file())
        download_button.pack(side=tk.RIGHT)
    
    def add_location_content(self):
        """Add location message content."""
        location_frame = tk.Frame(self.bubble_frame, bg=self.bubble_frame['bg'])
        location_frame.pack(pady=5)
        
        # Location icon and text
        location_icon = tk.Label(location_frame,
                                text="📍",
                                bg=self.bubble_frame['bg'],
                                font=self.fonts['emoji'])
        location_icon.pack(side=tk.LEFT, padx=(0, 10))
        
        location_text = self.message_data.get('location_name', 'Shared Location')
        location_label = tk.Label(location_frame,
                                 text=location_text,
                                 bg=self.bubble_frame['bg'],
                                 fg=self.colors['text_primary'],
                                 font=self.fonts['body'])
        location_label.pack(side=tk.LEFT)
    
    def add_contact_content(self):
        """Add contact message content."""
        contact_frame = tk.Frame(self.bubble_frame, bg=self.bubble_frame['bg'])
        contact_frame.pack(pady=5)
        
        # Contact icon
        contact_icon = tk.Label(contact_frame,
                               text="👤",
                               bg=self.bubble_frame['bg'],
                               font=self.fonts['emoji'])
        contact_icon.pack(side=tk.LEFT, padx=(0, 10))
        
        # Contact details
        contact_details_frame = tk.Frame(contact_frame, bg=self.bubble_frame['bg'])
        contact_details_frame.pack(side=tk.LEFT)
        
        contact_name = self.message_data.get('contact_name', 'Unknown Contact')
        contact_phone = self.message_data.get('contact_phone', '')
        
        name_label = tk.Label(contact_details_frame,
                             text=contact_name,
                             bg=self.bubble_frame['bg'],
                             fg=self.colors['text_primary'],
                             font=self.fonts['body'],
                             anchor=tk.W)
        name_label.pack(fill=tk.X)
        
        if contact_phone:
            phone_label = tk.Label(contact_details_frame,
                                  text=contact_phone,
                                  bg=self.bubble_frame['bg'],
                                  fg=self.colors['text_secondary'],
                                  font=self.fonts['small'],
                                  anchor=tk.W)
            phone_label.pack(fill=tk.X)
    
    def add_message_metadata(self):
        """Add message timestamp and status indicators."""
        metadata_frame = tk.Frame(self.bubble_frame, bg=self.bubble_frame['bg'])
        metadata_frame.pack(fill=tk.X, pady=(5, 0))
        
        # Timestamp
        timestamp = self.message_data.get('created_at', datetime.now())
        if isinstance(timestamp, str):
            try:
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            except:
                timestamp = datetime.now()
        
        time_text = timestamp.strftime("%H:%M")
        
        time_label = tk.Label(metadata_frame,
                             text=time_text,
                             bg=self.bubble_frame['bg'],
                             fg=self.colors['text_secondary'],
                             font=self.fonts['small'])
        time_label.pack(side=tk.RIGHT)
        
        # Message status (for own messages)
        if self.is_own_message:
            status = self.message_data.get('status', 'sent')
            status_icon = self.get_status_icon(status)
            
            status_label = tk.Label(metadata_frame,
                                   text=status_icon,
                                   bg=self.bubble_frame['bg'],
                                   fg=self.colors['text_secondary'],
                                   font=self.fonts['small'])
            status_label.pack(side=tk.RIGHT, padx=(0, 5))
        
        # Edited indicator
        if self.message_data.get('is_edited', False):
            edited_label = tk.Label(metadata_frame,
                                   text="edited",
                                   bg=self.bubble_frame['bg'],
                                   fg=self.colors['text_secondary'],
                                   font=self.fonts['small'])
            edited_label.pack(side=tk.RIGHT, padx=(0, 5))
    
    def get_status_icon(self, status: str) -> str:
        """Get status icon for message."""
        status_icons = {
            'sending': '🕐',
            'sent': '✓',
            'delivered': '✓✓',
            'read': '✓✓',  # Could be blue in actual WhatsApp
            'failed': '❌'
        }
        return status_icons.get(status, '✓')
    
    def add_reactions(self):
        """Add reaction emojis below the message."""
        reactions = self.message_data.get('reactions', [])
        if not reactions:
            return
        
        reactions_frame = tk.Frame(self.parent, bg=self.colors['chat_bg'])
        reactions_frame.pack(fill=tk.X, padx=20, pady=(0, 5))
        
        # Group reactions by emoji
        reaction_counts = {}
        for reaction in reactions:
            emoji_char = reaction.get('emoji', '👍')
            if emoji_char not in reaction_counts:
                reaction_counts[emoji_char] = 0
            reaction_counts[emoji_char] += 1
        
        # Display reaction buttons
        for emoji_char, count in reaction_counts.items():
            reaction_btn = tk.Button(reactions_frame,
                                   text=f"{emoji_char} {count}",
                                   bg=self.colors['background'],
                                   fg=self.colors['text_primary'],
                                   bd=1,
                                   relief='solid',
                                   font=self.fonts['small'],
                                   command=lambda e=emoji_char: self.toggle_reaction(e))
            reaction_btn.pack(side=tk.LEFT, padx=2)
    
    def add_context_menu(self):
        """Add right-click context menu."""
        def show_context_menu(event):
            context_menu = tk.Menu(self.parent, tearoff=0)
            
            # Reply option
            context_menu.add_command(label="Reply", 
                                   command=lambda: self.reply_to_message())
            
            # React option
            react_menu = tk.Menu(context_menu, tearoff=0)
            for emoji_char in ['👍', '❤️', '😂', '😮', '😢', '🙏']:
                react_menu.add_command(label=emoji_char,
                                     command=lambda e=emoji_char: self.add_reaction(e))
            context_menu.add_cascade(label="React", menu=react_menu)
            
            # Forward option
            context_menu.add_command(label="Forward", 
                                   command=lambda: self.forward_message())
            
            # Copy option (for text messages)
            if self.message_data.get('message_type') == 'text':
                context_menu.add_command(label="Copy", 
                                       command=lambda: self.copy_message())
            
            # Delete option (for own messages)
            if self.is_own_message:
                context_menu.add_separator()
                context_menu.add_command(label="Delete", 
                                       command=lambda: self.delete_message())
            
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
        
        self.bubble_frame.bind("<Button-3>", show_context_menu)  # Right click
    
    # Action methods (to be implemented)
    def view_full_image(self, image_url):
        """View image in full size."""
        # TODO: Implement image viewer
        pass
    
    def play_voice_message(self):
        """Play voice message."""
        # TODO: Implement voice playback
        pass
    
    def download_file(self):
        """Download file."""
        # TODO: Implement file download
        pass
    
    def toggle_reaction(self, emoji_char):
        """Toggle reaction on message."""
        if self.on_reaction_click:
            self.on_reaction_click(self.message_data['id'], emoji_char)
    
    def add_reaction(self, emoji_char):
        """Add reaction to message."""
        if self.on_reaction_click:
            self.on_reaction_click(self.message_data['id'], emoji_char)
    
    def reply_to_message(self):
        """Reply to this message."""
        if self.on_reply_click:
            self.on_reply_click(self.message_data)
    
    def forward_message(self):
        """Forward this message."""
        # TODO: Implement message forwarding
        pass
    
    def copy_message(self):
        """Copy message content to clipboard."""
        content = self.message_data.get('content', '')
        self.parent.clipboard_clear()
        self.parent.clipboard_append(content)
    
    def delete_message(self):
        """Delete this message."""
        # TODO: Implement message deletion
        pass
