import os
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, UploadFile, File, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

from .database import get_db, init_db, AsyncSessionLocal
from .auth import (
    authenticate_user, create_user, get_current_active_user, 
    create_access_token, create_refresh_token, verify_token,
    update_user_online_status, Token, RefreshTokenRequest
)
from .users import (
    User, UserCreate, UserResponse, UserLogin, UserUpdate,
    ChatRoom, ChatRoomCreate, ChatRoomResponse, ChatMember, ChatMemberResponse
)
from .messages import (
    Message, MessageCreate, MessageResponse, MessageUpdate, MessageWithSender,
    MessageType, FileUploadResponse
)
from .websocket_manager import manager, start_cleanup_task

# Create FastAPI app
app = FastAPI(
    title="Real-Time Chat API",
    description="A real-time chat application with WebSocket support",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Startup event
@app.on_event("startup")
async def startup_event():
    await init_db()
    start_cleanup_task()  # Start the WebSocket cleanup task
    print("Chat application started successfully!")

# Health check endpoint
@app.get("/")
async def root():
    return {"message": "Real-Time Chat API is running!"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "chat-api"}

# Authentication endpoints
@app.post("/auth/register", response_model=UserResponse)
async def register(user: UserCreate, db: AsyncSession = Depends(get_db)):
    """Register a new user."""
    try:
        db_user = await create_user(db, user)
        return UserResponse.from_orm(db_user)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )

@app.post("/auth/login", response_model=Token)
async def login(user_credentials: UserLogin, db: AsyncSession = Depends(get_db)):
    """Authenticate user and return JWT tokens."""
    user = await authenticate_user(db, user_credentials.username, user_credentials.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Update user online status
    await update_user_online_status(db, user.id, True)
    
    # Create tokens
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id}
    )
    refresh_token = create_refresh_token(
        data={"sub": user.username, "user_id": user.id}
    )
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer"
    )

@app.post("/auth/refresh", response_model=Token)
async def refresh_token(
    refresh_request: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """Refresh access token using refresh token."""
    token_data = verify_token(refresh_request.refresh_token, "refresh")
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    # Create new tokens
    access_token = create_access_token(
        data={"sub": token_data.username, "user_id": token_data.user_id}
    )
    refresh_token = create_refresh_token(
        data={"sub": token_data.username, "user_id": token_data.user_id}
    )
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer"
    )

@app.post("/auth/logout")
async def logout(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Logout user and update online status."""
    await update_user_online_status(db, current_user.id, False)
    return {"message": "Successfully logged out"}

# User endpoints
@app.get("/users/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """Get current user information."""
    return UserResponse.from_orm(current_user)

@app.put("/users/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update current user information."""
    update_data = user_update.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(current_user, field, value)
    
    await db.commit()
    await db.refresh(current_user)
    return UserResponse.from_orm(current_user)

@app.get("/users", response_model=List[UserResponse])
async def get_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get list of users."""
    result = await db.execute(
        select(User)
        .where(User.is_active == True)
        .offset(skip)
        .limit(limit)
    )
    users = result.scalars().all()
    return [UserResponse.from_orm(user) for user in users]

# Chat room endpoints
@app.post("/chat-rooms", response_model=ChatRoomResponse)
async def create_chat_room(
    room: ChatRoomCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new chat room."""
    db_room = ChatRoom(
        name=room.name,
        description=room.description,
        is_group=room.is_group,
        created_by=current_user.id
    )

    db.add(db_room)
    await db.commit()
    await db.refresh(db_room)

    # Add creator as admin member
    db_member = ChatMember(
        chat_room_id=db_room.id,
        user_id=current_user.id,
        is_admin=True
    )
    db.add(db_member)
    await db.commit()

    return ChatRoomResponse.from_orm(db_room)

@app.get("/chat-rooms", response_model=List[ChatRoomResponse])
async def get_user_chat_rooms(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get chat rooms for current user."""
    result = await db.execute(
        select(ChatRoom)
        .join(ChatMember)
        .where(ChatMember.user_id == current_user.id)
        .options(selectinload(ChatRoom.members))
    )
    rooms = result.scalars().all()

    room_responses = []
    for room in rooms:
        room_response = ChatRoomResponse.from_orm(room)
        room_response.member_count = len(room.members)
        room_responses.append(room_response)

    return room_responses

@app.post("/chat-rooms/{room_id}/join")
async def join_chat_room(
    room_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Join a chat room."""
    # Check if room exists
    result = await db.execute(select(ChatRoom).where(ChatRoom.id == room_id))
    room = result.scalar_one_or_none()
    if not room:
        raise HTTPException(status_code=404, detail="Chat room not found")

    # Check if already a member
    result = await db.execute(
        select(ChatMember).where(
            and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
        )
    )
    existing_member = result.scalar_one_or_none()
    if existing_member:
        raise HTTPException(status_code=400, detail="Already a member of this chat room")

    # Add as member
    db_member = ChatMember(
        chat_room_id=room_id,
        user_id=current_user.id,
        is_admin=False
    )
    db.add(db_member)
    await db.commit()

    return {"message": "Successfully joined chat room"}

@app.delete("/chat-rooms/{room_id}/leave")
async def leave_chat_room(
    room_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Leave a chat room."""
    result = await db.execute(
        select(ChatMember).where(
            and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
        )
    )
    member = result.scalar_one_or_none()
    if not member:
        raise HTTPException(status_code=404, detail="Not a member of this chat room")

    await db.delete(member)
    await db.commit()

    return {"message": "Successfully left chat room"}

@app.get("/chat-rooms/{room_id}/members", response_model=List[ChatMemberResponse])
async def get_chat_room_members(
    room_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get members of a chat room."""
    # Verify user is a member
    result = await db.execute(
        select(ChatMember).where(
            and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
        )
    )
    user_member = result.scalar_one_or_none()
    if not user_member:
        raise HTTPException(status_code=403, detail="Not a member of this chat room")

    # Get all members
    result = await db.execute(
        select(ChatMember, User)
        .join(User)
        .where(ChatMember.chat_room_id == room_id)
    )
    members_data = result.all()

    members = []
    for member, user in members_data:
        member_response = ChatMemberResponse(
            id=member.id,
            user_id=user.id,
            username=user.username,
            is_admin=member.is_admin,
            joined_at=member.joined_at
        )
        members.append(member_response)

    return members

# Message endpoints
@app.post("/chat-rooms/{room_id}/messages", response_model=MessageResponse)
async def send_message(
    room_id: int,
    message: MessageCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Send a message to a chat room."""
    # Verify user is a member of the chat room
    result = await db.execute(
        select(ChatMember).where(
            and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
        )
    )
    member = result.scalar_one_or_none()
    if not member:
        raise HTTPException(status_code=403, detail="Not a member of this chat room")

    # Create message
    db_message = Message(
        chat_room_id=room_id,
        sender_id=current_user.id,
        content=message.content,
        message_type=message.message_type
    )

    db.add(db_message)
    await db.commit()
    await db.refresh(db_message)

    # Broadcast message via WebSocket
    message_data = {
        "id": db_message.id,
        "chat_room_id": room_id,
        "sender_id": current_user.id,
        "sender_username": current_user.username,
        "content": message.content,
        "message_type": message.message_type,
        "created_at": db_message.created_at.isoformat()
    }

    await manager.broadcast_to_room(room_id, {
        "type": "message",
        "data": message_data
    })

    return MessageResponse.from_orm(db_message)

@app.get("/chat-rooms/{room_id}/messages", response_model=List[MessageWithSender])
async def get_messages(
    room_id: int,
    skip: int = 0,
    limit: int = 50,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get messages from a chat room."""
    # Verify user is a member
    result = await db.execute(
        select(ChatMember).where(
            and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
        )
    )
    member = result.scalar_one_or_none()
    if not member:
        raise HTTPException(status_code=403, detail="Not a member of this chat room")

    # Get messages with sender info
    result = await db.execute(
        select(Message, User)
        .join(User, Message.sender_id == User.id)
        .where(Message.chat_room_id == room_id)
        .order_by(Message.created_at.desc())
        .offset(skip)
        .limit(limit)
    )
    messages_data = result.all()

    messages = []
    for message, sender in messages_data:
        message_response = MessageWithSender(
            id=message.id,
            chat_room_id=message.chat_room_id,
            sender_id=message.sender_id,
            sender_username=sender.username,
            sender_avatar=sender.avatar_url,
            content=message.content,
            message_type=message.message_type,
            file_url=message.file_url,
            file_name=message.file_name,
            file_size=message.file_size,
            is_edited=message.is_edited,
            edited_at=message.edited_at,
            created_at=message.created_at
        )
        messages.append(message_response)

    return list(reversed(messages))  # Return in chronological order

@app.put("/messages/{message_id}", response_model=MessageResponse)
async def edit_message(
    message_id: int,
    message_update: MessageUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Edit a message."""
    result = await db.execute(select(Message).where(Message.id == message_id))
    message = result.scalar_one_or_none()

    if not message:
        raise HTTPException(status_code=404, detail="Message not found")

    if message.sender_id != current_user.id:
        raise HTTPException(status_code=403, detail="Can only edit your own messages")

    message.content = message_update.content
    message.is_edited = True
    message.edited_at = datetime.utcnow()

    await db.commit()
    await db.refresh(message)

    return MessageResponse.from_orm(message)

@app.delete("/messages/{message_id}")
async def delete_message(
    message_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete a message."""
    result = await db.execute(select(Message).where(Message.id == message_id))
    message = result.scalar_one_or_none()

    if not message:
        raise HTTPException(status_code=404, detail="Message not found")

    if message.sender_id != current_user.id:
        raise HTTPException(status_code=403, detail="Can only delete your own messages")

    await db.delete(message)
    await db.commit()

    return {"message": "Message deleted successfully"}

# WebSocket endpoint
@app.websocket("/ws/chat")
async def websocket_endpoint(websocket: WebSocket, token: str = None):
    """WebSocket endpoint for real-time chat."""
    if not token:
        await websocket.close(code=1008, reason="Token required")
        return

    # Verify token
    token_data = verify_token(token)
    if not token_data:
        await websocket.close(code=1008, reason="Invalid token")
        return

    user_id = token_data.user_id

    try:
        # Connect user
        await manager.connect(websocket, user_id)

        # Get user's chat rooms and join them
        async with AsyncSessionLocal() as db:
            result = await db.execute(
                select(ChatMember.chat_room_id)
                .where(ChatMember.user_id == user_id)
            )
            room_ids = [row[0] for row in result.all()]

            for room_id in room_ids:
                await manager.join_room(user_id, room_id)

        # Listen for messages
        while True:
            try:
                data = await websocket.receive_text()
                message_data = json.loads(data)

                message_type = message_data.get("type")

                if message_type == "typing_start":
                    room_id = message_data.get("room_id")
                    if room_id:
                        await manager.handle_typing_indicator(user_id, room_id, True)

                elif message_type == "typing_stop":
                    room_id = message_data.get("room_id")
                    if room_id:
                        await manager.handle_typing_indicator(user_id, room_id, False)

                elif message_type == "join_room":
                    room_id = message_data.get("room_id")
                    if room_id:
                        await manager.join_room(user_id, room_id)

                elif message_type == "leave_room":
                    room_id = message_data.get("room_id")
                    if room_id:
                        await manager.leave_room(user_id, room_id)

                elif message_type == "ping":
                    # Respond to ping to keep connection alive
                    await manager.send_personal_message(user_id, {
                        "type": "pong",
                        "timestamp": datetime.utcnow().isoformat()
                    })

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await manager.send_personal_message(user_id, {
                    "type": "error",
                    "message": "Invalid JSON format"
                })
            except Exception as e:
                await manager.send_personal_message(user_id, {
                    "type": "error",
                    "message": f"Error processing message: {str(e)}"
                })

    except Exception as e:
        print(f"WebSocket error for user {user_id}: {e}")
    finally:
        await manager.disconnect(user_id)

# File upload endpoints
@app.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Upload a file."""
    # Create uploads directory if it doesn't exist
    upload_dir = "uploads"
    os.makedirs(upload_dir, exist_ok=True)

    # Generate unique filename
    import uuid
    file_extension = os.path.splitext(file.filename)[1] if file.filename else ""
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(upload_dir, unique_filename)

    try:
        # Save file
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # Determine message type
        message_type = MessageType.FILE
        if file.content_type and file.content_type.startswith("image/"):
            message_type = MessageType.IMAGE

        return FileUploadResponse(
            file_url=f"/files/{unique_filename}",
            file_name=file.filename or unique_filename,
            file_size=len(content),
            message_type=message_type
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"File upload failed: {str(e)}"
        )

@app.get("/files/{filename}")
async def get_file(filename: str):
    """Serve uploaded files."""
    from fastapi.responses import FileResponse
    file_path = os.path.join("uploads", filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")

    return FileResponse(file_path)
