services:
  - type: web
    name: chat-api
    env: python
    buildCommand: "cd backend && pip install -r requirements.txt"
    startCommand: "cd backend && uvicorn app.main:app --host 0.0.0.0 --port $PORT"
    plan: free
    healthCheckPath: /health
    envVars:
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        fromDatabase:
          name: chat-db
          property: connectionString
      - key: PYTHONPATH
        value: /opt/render/project/src/backend

databases:
  - name: chat-db
    databaseName: chatapp
    user: chatuser
    plan: free
