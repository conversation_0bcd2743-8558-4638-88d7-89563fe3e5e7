#!/usr/bin/env python3
"""
Test the FULLY ADVANCED WhatsApp-like chat application.
This tests ALL features including voice/video calling, file sharing, status updates, etc.
"""

import os
import sys
import subprocess
import time
import requests
import threading

def check_dependencies():
    """Check if all required dependencies are installed."""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'tkinter', 'PIL', 'pygame', 'cv2', 'pyaudio', 'plyer', 'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'PIL':
                from PIL import Image, ImageTk
            elif package == 'pygame':
                import pygame
            elif package == 'cv2':
                import cv2
            elif package == 'pyaudio':
                import pyaudio
            elif package == 'plyer':
                from plyer import notification
            elif package == 'requests':
                import requests
            
            print(f"✅ {package} - Available")
        except ImportError:
            print(f"❌ {package} - Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install with:")
        for package in missing_packages:
            if package == 'PIL':
                print("  pip install Pillow")
            elif package == 'cv2':
                print("  pip install opencv-python")
            elif package == 'tkinter':
                print("  tkinter is usually included with Python")
            else:
                print(f"  pip install {package}")
        return False
    
    print("✅ All dependencies available")
    return True

def check_backend():
    """Check if backend is running."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=3)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """Start backend if needed."""
    if check_backend():
        print("✅ Backend is already running")
        return True
    
    print("🚀 Starting backend server...")
    backend_dir = os.path.join(os.path.dirname(__file__), "backend")
    
    try:
        if os.name == 'nt':  # Windows
            subprocess.Popen(
                ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"],
                cwd=backend_dir,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
        else:
            subprocess.Popen(
                ["python3", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"],
                cwd=backend_dir
            )
        
        # Wait for backend
        for i in range(30):
            if check_backend():
                print("✅ Backend started successfully")
                return True
            time.sleep(1)
        
        print("❌ Backend failed to start")
        return False
        
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        return False

def setup_test_environment():
    """Set up test environment."""
    print("🔧 Setting up advanced test environment...")
    
    # Create test users
    test_users = [
        {"username": "alice", "email": "<EMAIL>", "password": "testpass123", "full_name": "Alice Johnson"},
        {"username": "bob", "email": "<EMAIL>", "password": "testpass123", "full_name": "Bob Smith"},
        {"username": "charlie", "email": "<EMAIL>", "password": "testpass123", "full_name": "Charlie Brown"},
        {"username": "diana", "email": "<EMAIL>", "password": "testpass123", "full_name": "Diana Prince"}
    ]
    
    for user_data in test_users:
        try:
            response = requests.post("http://localhost:8001/auth/register", json=user_data)
            if response.status_code == 200:
                print(f"✅ Created user: {user_data['username']}")
            else:
                print(f"⚠️ User {user_data['username']} might already exist")
        except Exception as e:
            print(f"❌ Error creating user {user_data['username']}: {e}")
    
    # Set up general chat room
    try:
        backend_dir = os.path.join(os.path.dirname(__file__), "backend")
        result = subprocess.run(
            ["python", "setup_general_chat.py"],
            cwd=backend_dir,
            capture_output=True,
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print("✅ General chat room setup completed")
        else:
            print("⚠️ General chat setup had issues")
    except Exception as e:
        print(f"❌ Error setting up general chat: {e}")

def test_api_endpoints():
    """Test all API endpoints."""
    print("🧪 Testing advanced API endpoints...")
    
    try:
        # Test login
        response = requests.post("http://localhost:8001/auth/login", json={
            "username": "alice",
            "password": "testpass123"
        })
        
        if response.status_code == 200:
            token = response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ Authentication API working")
            
            # Test all endpoints
            endpoints_to_test = [
                ("GET", "/chat-rooms", "Chat rooms"),
                ("GET", "/users", "Users list"),
                ("GET", "/contacts", "Contacts"),
                ("GET", "/status-updates", "Status updates")
            ]
            
            for method, endpoint, name in endpoints_to_test:
                try:
                    if method == "GET":
                        resp = requests.get(f"http://localhost:8001{endpoint}", headers=headers)
                    
                    if resp.status_code == 200:
                        data = resp.json()
                        print(f"✅ {name} API working ({len(data) if isinstance(data, list) else 'OK'})")
                    else:
                        print(f"⚠️ {name} API returned {resp.status_code}")
                except Exception as e:
                    print(f"❌ {name} API error: {e}")
            
            # Test message sending
            rooms_response = requests.get("http://localhost:8001/chat-rooms", headers=headers)
            if rooms_response.status_code == 200:
                rooms = rooms_response.json()
                if rooms:
                    room_id = rooms[0]['id']
                    message_response = requests.post(
                        f"http://localhost:8001/chat-rooms/{room_id}/messages",
                        json={"content": "Advanced test message with emojis 🚀💬✨"},
                        headers=headers
                    )
                    if message_response.status_code == 200:
                        print("✅ Advanced message sending API working")
                    else:
                        print(f"❌ Message sending failed: {message_response.status_code}")
                        
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API test error: {e}")

def show_advanced_test_instructions():
    """Show comprehensive testing instructions."""
    print("\n" + "=" * 80)
    print("🎯 ADVANCED WHATSAPP-LIKE CHAT APPLICATION - TESTING GUIDE")
    print("=" * 80)
    print("\n📱 WHAT YOU'LL SEE:")
    print("✅ Modern WhatsApp-like interface with 3-panel layout")
    print("✅ Sidebar with tabs: Chats, Status, Calls, Contacts")
    print("✅ Main chat area with advanced message bubbles")
    print("✅ All buttons and features are FULLY FUNCTIONAL")
    print("\n🎯 COMPREHENSIVE TESTING CHECKLIST:")
    
    print("\n📋 1. BASIC MESSAGING:")
    print("   • Login with: alice / testpass123")
    print("   • Click 'General Chat' in sidebar")
    print("   • Send text messages with emojis")
    print("   • See message status indicators (✓, ✓✓)")
    print("   • Right-click messages for context menu")
    
    print("\n🎤 2. VOICE MESSAGES:")
    print("   • Hold the microphone button to record")
    print("   • Release to send voice message")
    print("   • Click play button on voice messages")
    
    print("\n📞 3. VOICE & VIDEO CALLS:")
    print("   • Click phone icon (📞) for voice call")
    print("   • Click video icon (📹) for video call")
    print("   • Test call controls: mute, speaker, end call")
    print("   • Try video toggle during video calls")
    
    print("\n📎 4. FILE SHARING:")
    print("   • Click attachment button (📎)")
    print("   • Test: Camera, Photos, Documents, Audio")
    print("   • Send location and contacts")
    print("   • Download shared files")
    
    print("\n📱 5. STATUS UPDATES:")
    print("   • Click 'Status' tab in sidebar")
    print("   • Create text/photo/video status")
    print("   • View others' status updates")
    print("   • Check 24-hour expiry")
    
    print("\n😊 6. EMOJI & REACTIONS:")
    print("   • Click emoji button (😊) in message input")
    print("   • Browse emoji categories")
    print("   • Add emojis to messages")
    print("   • React to messages (right-click)")
    
    print("\n👥 7. CONTACTS & GROUPS:")
    print("   • Click 'Contacts' tab")
    print("   • Add new contacts")
    print("   • Create new groups")
    print("   • Manage group settings")
    
    print("\n🔍 8. SEARCH & NAVIGATION:")
    print("   • Use search bar to find chats")
    print("   • Search within conversations")
    print("   • Navigate between different sections")
    
    print("\n⚙️ 9. SETTINGS & CUSTOMIZATION:")
    print("   • Click menu (⋮) for settings")
    print("   • Toggle dark/light mode")
    print("   • Notification settings")
    print("   • Profile management")
    
    print("\n🌐 10. MULTI-USER TESTING:")
    print("   • Open second client instance")
    print("   • Login with: bob / testpass123")
    print("   • Test real-time messaging")
    print("   • Try voice/video calls between users")
    print("   • Test all features across multiple users")
    
    print("\n🎊 ADVANCED FEATURES TO TEST:")
    print("   ✅ Message encryption indicators")
    print("   ✅ Typing indicators")
    print("   ✅ Online/offline status")
    print("   ✅ Read receipts")
    print("   ✅ Message forwarding")
    print("   ✅ Chat pinning and muting")
    print("   ✅ Group administration")
    print("   ✅ Broadcast messages")
    print("   ✅ Desktop notifications")
    print("   ✅ File preview and download")
    
    print("\n🚀 EXPECTED RESULTS:")
    print("   • ALL features should work without 'coming soon' messages")
    print("   • Professional WhatsApp-like user experience")
    print("   • Real-time communication between users")
    print("   • Smooth animations and interactions")
    print("   • No crashes or error dialogs")
    
    print("\n" + "=" * 80)

def start_advanced_client():
    """Start the advanced chat client."""
    print("🖥️ Starting ADVANCED WhatsApp-like chat client...")
    
    client_dir = os.path.join(os.path.dirname(__file__), "client")
    
    try:
        subprocess.run(["python", "main.py"], cwd=client_dir)
    except KeyboardInterrupt:
        print("\n👋 Advanced client testing completed")
    except Exception as e:
        print(f"❌ Client error: {e}")

def main():
    """Main test function."""
    print("💬 ADVANCED WHATSAPP-LIKE CHAT APPLICATION TEST")
    print("=" * 60)
    print("🎯 Goal: Test ALL advanced features including calling, file sharing, status updates")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies before proceeding")
        return
    
    # Start backend
    if not start_backend():
        print("❌ Cannot proceed without backend")
        return
    
    # Setup test environment
    setup_test_environment()
    
    # Test API endpoints
    test_api_endpoints()
    
    # Show instructions
    show_advanced_test_instructions()
    
    # Ask to start client
    choice = input("\nStart the ADVANCED chat client? (y/n): ").strip().lower()
    
    if choice == 'y':
        start_advanced_client()
    else:
        print("\n👋 Setup complete! Start client manually:")
        print("cd client && python main.py")
        print("\nAdvanced test accounts:")
        print("• alice / testpass123")
        print("• bob / testpass123")
        print("• charlie / testpass123")
        print("• diana / testpass123")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Advanced test interrupted")
    except Exception as e:
        print(f"❌ Test error: {e}")
