from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from enum import Enum

# Message Types Enum
class MessageType(str, Enum):
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    SYSTEM = "system"

# SQLAlchemy Message Model
class Message(Base):
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    chat_room_id = Column(Integer, ForeignKey("chat_rooms.id"), nullable=False)
    sender_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    content = Column(Text, nullable=False)
    message_type = Column(String(20), default=MessageType.TEXT)
    file_url = Column(String(255), nullable=True)  # For file/image messages
    file_name = Column(String(255), nullable=True)
    file_size = Column(Integer, nullable=True)
    is_edited = Column(Boolean, default=False)
    edited_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    sender = relationship("User", foreign_keys=[sender_id], back_populates="sent_messages")
    chat_room = relationship("ChatRoom", back_populates="messages")
    read_receipts = relationship("MessageReadReceipt", back_populates="message")

# Message Read Receipts
class MessageReadReceipt(Base):
    __tablename__ = "message_read_receipts"
    
    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(Integer, ForeignKey("messages.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    read_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    message = relationship("Message", back_populates="read_receipts")

# Typing Indicators
class TypingIndicator(Base):
    __tablename__ = "typing_indicators"
    
    id = Column(Integer, primary_key=True, index=True)
    chat_room_id = Column(Integer, ForeignKey("chat_rooms.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)

# Pydantic Models for API
class MessageBase(BaseModel):
    content: str
    message_type: MessageType = MessageType.TEXT

class MessageCreate(MessageBase):
    chat_room_id: int

class MessageUpdate(BaseModel):
    content: str

class MessageResponse(MessageBase):
    id: int
    chat_room_id: int
    sender_id: int
    sender_username: Optional[str] = None
    file_url: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    is_edited: bool
    edited_at: Optional[datetime] = None
    created_at: datetime
    read_by: Optional[List[int]] = []  # List of user IDs who read the message
    
    class Config:
        from_attributes = True

class MessageWithSender(MessageResponse):
    sender_username: str
    sender_avatar: Optional[str] = None

class TypingIndicatorResponse(BaseModel):
    chat_room_id: int
    user_id: int
    username: str
    started_at: datetime
    expires_at: datetime
    
    class Config:
        from_attributes = True

class FileUploadResponse(BaseModel):
    file_url: str
    file_name: str
    file_size: int
    message_type: MessageType

# WebSocket Message Types
class WSMessageType(str, Enum):
    MESSAGE = "message"
    TYPING_START = "typing_start"
    TYPING_STOP = "typing_stop"
    USER_JOINED = "user_joined"
    USER_LEFT = "user_left"
    USER_ONLINE = "user_online"
    USER_OFFLINE = "user_offline"
    MESSAGE_READ = "message_read"
    ERROR = "error"

class WSMessage(BaseModel):
    type: WSMessageType
    data: dict
    chat_room_id: Optional[int] = None
    timestamp: datetime = datetime.utcnow()

class WSTypingMessage(BaseModel):
    type: WSMessageType
    chat_room_id: int
    user_id: int
    username: str
    timestamp: datetime = datetime.utcnow()

class WSPresenceMessage(BaseModel):
    type: WSMessageType
    user_id: int
    username: str
    is_online: bool
    timestamp: datetime = datetime.utcnow()

class WSErrorMessage(BaseModel):
    type: WSMessageType = WSMessageType.ERROR
    error: str
    timestamp: datetime = datetime.utcnow()
