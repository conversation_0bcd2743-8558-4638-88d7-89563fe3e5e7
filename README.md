# Real-Time Chat Application 💬

A fully functional, real-time chat application built with Python, featuring a modern desktop GUI frontend and a powerful FastAPI backend with WebSocket support for internet-based messaging.

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)
![Tkinter](https://img.shields.io/badge/GUI-Tkinter-orange.svg)
![WebSocket](https://img.shields.io/badge/WebSocket-Real--time-red.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## ✨ Features

### 🔥 Core Features
- **Real-time messaging** over the internet using WebSockets
- **Modern desktop GUI** built with Tkin<PERSON> for cross-platform compatibility
- **JWT-based authentication** with secure user login and registration
- **Private chats (one-on-one)** and **group chats** support
- **Typing indicators** and **online/offline presence** status
- **Message history** stored in SQLite/PostgreSQL database
- **File and image upload** capability
- **Cloud deployment ready** for global access

### 🎯 Advanced Features
- **Message timestamps** and delivery receipts
- **Auto-reconnection** for WebSocket connections
- **Responsive UI** with real-time updates
- **Cross-platform compatibility** (Windows, macOS, Linux)
- **Secure password hashing** with bcrypt
- **Token refresh** mechanism
- **Health monitoring** and status indicators
- **Docker support** for easy deployment

## 📁 Project Structure

```
real_time_chat_internet/
├── backend/
│   ├── app/
│   │   ├── main.py                # FastAPI app + WebSocket endpoints
│   │   ├── auth.py                # JWT login/register
│   │   ├── users.py               # User model and operations
│   │   ├── messages.py            # Message handling logic
│   │   ├── database.py            # SQLAlchemy DB setup
│   │   ├── websocket_manager.py   # Manage WebSocket connections and rooms
│   ├── requirements.txt
│   └── Procfile                   # For deployment (e.g., on Render)
├── client/
│   ├── main.py                   # Main GUI interface (Tkinter)
│   ├── login.py                  # Login and registration screens
│   ├── chat_window.py            # Chat UI with send/receive logic
│   ├── websocket_client.py       # WebSocket connection to server
│   ├── helpers.py                # Utils (token handling, local state)
│   ├── assets/                   # Icons, sounds, media
│   └── requirements.txt
├── README.md
└── .gitignore
```

## 🚀 Quick Start

### Automatic Installation (Recommended)

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd Chat\ Application
   ```

2. **Run the installer:**
   ```bash
   python install.py
   ```

3. **Start the development environment:**
   ```bash
   python start_dev.py
   ```

That's it! The installer will set up everything automatically and start both the backend and client.

### Manual Installation

#### Prerequisites
- Python 3.8 or higher
- pip package manager
- tkinter (usually included with Python)

#### Backend Setup

1. **Navigate to the backend directory:**
   ```bash
   cd backend
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the FastAPI server:**
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

#### Client Setup

1. **Navigate to the client directory:**
   ```bash
   cd client
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the client application:**
   ```bash
   python main.py
   ```

### Docker Setup (Alternative)

1. **Start with Docker Compose:**
   ```bash
   docker-compose up --build
   ```

2. **Run the client separately:**
   ```bash
   cd client && python main.py
   ```

## 🧪 Testing

### Test the Backend API

Run the automated test suite:
```bash
python backend/test_api.py
```

This will test all API endpoints including:
- User registration and authentication
- Chat room creation and management
- Message sending and retrieval
- WebSocket connections

### Manual Testing

1. **Start the backend server**
2. **Open your browser** and go to `http://localhost:8000/docs`
3. **Use the interactive API documentation** to test endpoints
4. **Launch the client** and test the GUI functionality

## 🌍 Deployment

The application is ready for deployment on various cloud platforms:

### Supported Platforms
- **[Render](https://render.com)** (Recommended - Free tier available)
- **[Railway](https://railway.app)** (Simple deployment)
- **[Heroku](https://heroku.com)** (Classic platform)
- **[DigitalOcean](https://digitalocean.com)** (VPS/App Platform)
- **[AWS](https://aws.amazon.com)** (Elastic Beanstalk/ECS)

### Quick Deploy to Render

1. Fork this repository
2. Connect to Render
3. Deploy automatically with `render.yaml`

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

## 📱 Usage Guide

### First Time Setup

1. **Start the application** using `python start_dev.py`
2. **Register a new account** in the client application
3. **Create or join chat rooms**
4. **Start messaging in real-time!**

### Features Overview

- **🔐 Authentication**: Secure login with JWT tokens
- **💬 Real-time Chat**: Instant messaging with WebSockets
- **👥 Group Chats**: Create and manage group conversations
- **📁 File Sharing**: Upload and share files/images
- **⌨️ Typing Indicators**: See when others are typing
- **🟢 Online Status**: View who's currently online
- **📱 Cross-platform**: Works on Windows, macOS, and Linux

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite+aiosqlite:///./chat_app.db
DEBUG=True
CORS_ORIGINS=*
```

#### Client (.env)
```env
API_BASE_URL=http://localhost:8000
```

### Database Options

- **SQLite** (Development): No setup required
- **PostgreSQL** (Production): Full-featured database
- **Docker**: Automated setup with docker-compose

## 🛠️ Development

### Project Structure
```
real_time_chat_internet/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── main.py         # Main application
│   │   ├── auth.py         # Authentication
│   │   ├── users.py        # User models
│   │   ├── messages.py     # Message models
│   │   ├── database.py     # Database setup
│   │   └── websocket_manager.py  # WebSocket handling
│   ├── requirements.txt
│   └── Dockerfile
├── client/                 # Tkinter client
│   ├── main.py            # Main GUI application
│   ├── login.py           # Login interface
│   ├── chat_window.py     # Chat interface
│   ├── websocket_client.py # WebSocket client
│   └── helpers.py         # Utility functions
├── install.py             # Automated installer
├── start_dev.py          # Development starter
└── docker-compose.yml    # Docker configuration
```

### Adding New Features

1. **Backend**: Add new endpoints in `backend/app/main.py`
2. **Database**: Update models in `backend/app/users.py` or `messages.py`
3. **Client**: Modify GUI in `client/chat_window.py`
4. **WebSocket**: Update handlers in `websocket_manager.py`

## 🐛 Troubleshooting

### Common Issues

1. **Port 8000 already in use**
   ```bash
   # Find and kill the process
   lsof -ti:8000 | xargs kill -9
   ```

2. **WebSocket connection failed**
   - Check if backend is running
   - Verify firewall settings
   - Ensure correct URL in client config

3. **Database errors**
   - Delete `chat_app.db` to reset
   - Check DATABASE_URL format
   - Verify database permissions

4. **Import errors**
   - Run `python install.py` again
   - Check Python version (3.8+ required)
   - Verify virtual environment activation

### Getting Help

- Check the [Issues](../../issues) page
- Review logs in the terminal
- Test API with `python backend/test_api.py`
- Verify installation with `python install.py`

## 🤝 Contributing

We welcome contributions! Here's how to get started:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature-name`
3. **Make your changes** and test thoroughly
4. **Submit a pull request** with a clear description

### Development Guidelines

- Follow PEP 8 for Python code style
- Add tests for new features
- Update documentation as needed
- Test on multiple platforms if possible

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **FastAPI** for the excellent web framework
- **Tkinter** for the GUI framework
- **WebSockets** for real-time communication
- **SQLAlchemy** for database management

---

**Happy Chatting! 💬🚀**

For more detailed information, see:
- [DEPLOYMENT.md](DEPLOYMENT.md) - Deployment guide
- [Backend API Documentation](http://localhost:8000/docs) - Interactive API docs
