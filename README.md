# Real-Time Chat Application

A fully functional, real-time chat application built with Python, featuring a modern desktop GUI frontend and a powerful FastAPI backend with WebSocket support for internet-based messaging.

## 🚀 Features

- **Real-time messaging** over the internet using WebSockets
- **Modern desktop GUI** built with <PERSON><PERSON><PERSON> for cross-platform compatibility
- **JWT-based authentication** with secure user login and registration
- **Private chats (one-on-one)** and **group chats** support
- **Typing indicators** and **online/offline presence** status
- **Message history** stored in SQLite/PostgreSQL database
- **File and image upload** capability
- **Cloud deployment ready** for global access

## 📁 Project Structure

```
real_time_chat_internet/
├── backend/
│   ├── app/
│   │   ├── main.py                # FastAPI app + WebSocket endpoints
│   │   ├── auth.py                # JWT login/register
│   │   ├── users.py               # User model and operations
│   │   ├── messages.py            # Message handling logic
│   │   ├── database.py            # SQLAlchemy DB setup
│   │   ├── websocket_manager.py   # Manage WebSocket connections and rooms
│   ├── requirements.txt
│   └── Procfile                   # For deployment (e.g., on Render)
├── client/
│   ├── main.py                   # Main GUI interface (Tkinter)
│   ├── login.py                  # Login and registration screens
│   ├── chat_window.py            # Chat UI with send/receive logic
│   ├── websocket_client.py       # WebSocket connection to server
│   ├── helpers.py                # Utils (token handling, local state)
│   ├── assets/                   # Icons, sounds, media
│   └── requirements.txt
├── README.md
└── .gitignore
```

## 🛠️ Installation & Setup

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the FastAPI server:
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

### Client Setup

1. Navigate to the client directory:
   ```bash
   cd client
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the client application:
   ```bash
   python main.py
   ```

## 🌍 Deployment

The backend is designed to be deployed on cloud platforms like:
- [Render](https://render.com)
- [Railway](https://railway.app)
- [Heroku](https://heroku.com)
- VPS (DigitalOcean, AWS, etc.)

## 🔧 Configuration

- WebSocket endpoint: `wss://yourdomain.com/ws/chat`
- CORS and HTTPS configured for global access
- JWT tokens for secure authentication
- SQLite for development, PostgreSQL for production

## 📝 Usage

1. Start the backend server
2. Launch the client application
3. Register a new account or login with existing credentials
4. Start chatting in real-time!

## 🎯 Advanced Features

- Message timestamps and delivery receipts
- Notification system for new messages
- Dark/light mode toggle
- File/image transfer
- Admin/moderator roles for group chats

## 🤝 Contributing

Feel free to contribute to this project by submitting issues or pull requests.

## 📄 License

This project is open source and available under the MIT License.
