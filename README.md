# Real-Time Chat Application 💬 - **WhatsApp-Like Features**

A **fully modernized, WhatsApp-like chat application** built with Python, featuring an advanced desktop GUI frontend and a powerful FastAPI backend with comprehensive real-time messaging capabilities.

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)
![Tkinter](https://img.shields.io/badge/GUI-Modern_Tkinter-orange.svg)
![WebSocket](https://img.shields.io/badge/WebSocket-Real--time-red.svg)
![WhatsApp](https://img.shields.io/badge/Features-WhatsApp--like-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## 🚀 **NEW: WhatsApp-Like Features**

This application now includes **ALL major WhatsApp features** with a modern, professional interface!

### 💬 **Advanced Messaging**
- **Message Reactions** - React with emojis (👍❤️😂😮😢🙏)
- **Reply to Messages** - Quote and reply to specific messages
- **Message Forwarding** - Forward messages to other chats
- **Message Editing** - Edit sent messages with "edited" indicator
- **Message Status** - Sent (✓), Delivered (✓✓), Read (✓✓)
- **Voice Messages** - Record and send voice notes with waveform
- **Media Sharing** - Photos, videos, documents with previews

### 🎨 **Modern UI/UX**
- **WhatsApp-like Design** - Green theme with chat bubbles
- **Profile Pictures** - Circular avatars and user profiles
- **Status Updates** - 24-hour disappearing stories
- **Typing Indicators** - Real-time typing status
- **Online Presence** - See who's online and last seen
- **Dark/Light Themes** - Modern color schemes

### 🔔 **Smart Notifications**
- **Desktop Notifications** - System notifications with preview
- **Sound Alerts** - Different sounds for different events
- **Popup Notifications** - Custom in-app notifications
- **Quiet Hours** - Disable notifications during set times
- **Notification Settings** - Granular control over all alerts

### 👥 **Contact & Social Features**
- **Contact Management** - Add, block, favorite contacts
- **User Profiles** - Bio, status message, profile pictures
- **Status Stories** - Share text/media status updates
- **Privacy Controls** - Control who sees your information
- **Group Administration** - Advanced group management

## ✨ Core Features

### 🔥 Real-time Communication
- **WebSocket messaging** with auto-reconnection
- **Cross-platform compatibility** (Windows, macOS, Linux)
- **JWT-based authentication** with secure login/registration
- **Private & group chats** with member management
- **Message history** with SQLite/PostgreSQL storage
- **File upload** with drag-and-drop support
- **Cloud deployment ready** for global access

### 🎯 Advanced Capabilities
- **Voice recording** with real-time waveform visualization
- **Media compression** and optimization
- **Message search** and filtering
- **Chat backup** and export functionality
- **Multi-language support** with emoji integration
- **Performance monitoring** and health checks
- **Docker containerization** for easy deployment

## 📁 Project Structure

```
real_time_chat_internet/
├── backend/
│   ├── app/
│   │   ├── main.py                # FastAPI app + WebSocket endpoints
│   │   ├── auth.py                # JWT login/register
│   │   ├── users.py               # User model and operations
│   │   ├── messages.py            # Message handling logic
│   │   ├── database.py            # SQLAlchemy DB setup
│   │   ├── websocket_manager.py   # Manage WebSocket connections and rooms
│   ├── requirements.txt
│   └── Procfile                   # For deployment (e.g., on Render)
├── client/
│   ├── main.py                   # Main GUI interface (Tkinter)
│   ├── login.py                  # Login and registration screens
│   ├── chat_window.py            # Chat UI with send/receive logic
│   ├── websocket_client.py       # WebSocket connection to server
│   ├── helpers.py                # Utils (token handling, local state)
│   ├── assets/                   # Icons, sounds, media
│   └── requirements.txt
├── README.md
└── .gitignore
```

## 🚀 Quick Start

### Automatic Installation (Recommended)

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd Chat\ Application
   ```

2. **Run the installer:**
   ```bash
   python install.py
   ```

3. **Start the development environment:**
   ```bash
   python start_dev.py
   ```

That's it! The installer will set up everything automatically and start both the backend and client.

### Manual Installation

#### Prerequisites
- Python 3.8 or higher
- pip package manager
- tkinter (usually included with Python)

#### Backend Setup

1. **Navigate to the backend directory:**
   ```bash
   cd backend
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the FastAPI server:**
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

#### Client Setup

1. **Navigate to the client directory:**
   ```bash
   cd client
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the client application:**
   ```bash
   python main.py
   ```

### Docker Setup (Alternative)

1. **Start with Docker Compose:**
   ```bash
   docker-compose up --build
   ```

2. **Run the client separately:**
   ```bash
   cd client && python main.py
   ```

## 🧪 Testing

### Test the Backend API

Run the automated test suite:
```bash
python backend/test_api.py
```

This will test all API endpoints including:
- User registration and authentication
- Chat room creation and management
- Message sending and retrieval
- WebSocket connections

### Manual Testing

1. **Start the backend server**
2. **Open your browser** and go to `http://localhost:8000/docs`
3. **Use the interactive API documentation** to test endpoints
4. **Launch the client** and test the GUI functionality

## 🌍 Deployment

The application is ready for deployment on various cloud platforms:

### Supported Platforms
- **[Render](https://render.com)** (Recommended - Free tier available)
- **[Railway](https://railway.app)** (Simple deployment)
- **[Heroku](https://heroku.com)** (Classic platform)
- **[DigitalOcean](https://digitalocean.com)** (VPS/App Platform)
- **[AWS](https://aws.amazon.com)** (Elastic Beanstalk/ECS)

### Quick Deploy to Render

1. Fork this repository
2. Connect to Render
3. Deploy automatically with `render.yaml`

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

## 📱 Usage Guide

### First Time Setup

1. **Start the application** using `python start_dev.py`
2. **Register a new account** in the client application
3. **Create or join chat rooms**
4. **Start messaging in real-time!**

### Features Overview

- **🔐 Authentication**: Secure login with JWT tokens
- **💬 Real-time Chat**: Instant messaging with WebSockets
- **👥 Group Chats**: Create and manage group conversations
- **📁 File Sharing**: Upload and share files/images
- **⌨️ Typing Indicators**: See when others are typing
- **🟢 Online Status**: View who's currently online
- **📱 Cross-platform**: Works on Windows, macOS, and Linux

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite+aiosqlite:///./chat_app.db
DEBUG=True
CORS_ORIGINS=*
```

#### Client (.env)
```env
API_BASE_URL=http://localhost:8000
```

### Database Options

- **SQLite** (Development): No setup required
- **PostgreSQL** (Production): Full-featured database
- **Docker**: Automated setup with docker-compose

## 🛠️ Development

### Project Structure
```
real_time_chat_internet/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── main.py         # Main application
│   │   ├── auth.py         # Authentication
│   │   ├── users.py        # User models
│   │   ├── messages.py     # Message models
│   │   ├── database.py     # Database setup
│   │   └── websocket_manager.py  # WebSocket handling
│   ├── requirements.txt
│   └── Dockerfile
├── client/                 # Tkinter client
│   ├── main.py            # Main GUI application
│   ├── login.py           # Login interface
│   ├── chat_window.py     # Chat interface
│   ├── websocket_client.py # WebSocket client
│   └── helpers.py         # Utility functions
├── install.py             # Automated installer
├── start_dev.py          # Development starter
└── docker-compose.yml    # Docker configuration
```

### Adding New Features

1. **Backend**: Add new endpoints in `backend/app/main.py`
2. **Database**: Update models in `backend/app/users.py` or `messages.py`
3. **Client**: Modify GUI in `client/chat_window.py`
4. **WebSocket**: Update handlers in `websocket_manager.py`

## 🐛 Troubleshooting

### Common Issues

1. **Port 8000 already in use**
   ```bash
   # Find and kill the process
   lsof -ti:8000 | xargs kill -9
   ```

2. **WebSocket connection failed**
   - Check if backend is running
   - Verify firewall settings
   - Ensure correct URL in client config

3. **Database errors**
   - Delete `chat_app.db` to reset
   - Check DATABASE_URL format
   - Verify database permissions

4. **Import errors**
   - Run `python install.py` again
   - Check Python version (3.8+ required)
   - Verify virtual environment activation

### Getting Help

- Check the [Issues](../../issues) page
- Review logs in the terminal
- Test API with `python backend/test_api.py`
- Verify installation with `python install.py`

## 🤝 Contributing

We welcome contributions! Here's how to get started:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature-name`
3. **Make your changes** and test thoroughly
4. **Submit a pull request** with a clear description

### Development Guidelines

- Follow PEP 8 for Python code style
- Add tests for new features
- Update documentation as needed
- Test on multiple platforms if possible

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **FastAPI** for the excellent web framework
- **Tkinter** for the GUI framework
- **WebSockets** for real-time communication
- **SQLAlchemy** for database management

---

**Happy Chatting! 💬🚀**

For more detailed information, see:
- [DEPLOYMENT.md](DEPLOYMENT.md) - Deployment guide
- [Backend API Documentation](http://localhost:8000/docs) - Interactive API docs
