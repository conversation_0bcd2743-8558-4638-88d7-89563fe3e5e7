#!/usr/bin/env python3
"""
Test the WORKING chat application - NO MORE BROKEN FEATURES!
This verifies that chat room selection and messaging ACTUALLY WORKS.
"""

import os
import sys
import subprocess
import time
import requests
import threading

def check_backend():
    """Check if backend is running."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=3)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """Start backend if needed."""
    if check_backend():
        print("✅ Backend is already running")
        return True
    
    print("🚀 Starting backend server...")
    backend_dir = os.path.join(os.path.dirname(__file__), "backend")
    
    try:
        if os.name == 'nt':  # Windows
            subprocess.Popen(
                ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"],
                cwd=backend_dir,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
        else:
            subprocess.Popen(
                ["python3", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"],
                cwd=backend_dir
            )
        
        # Wait for backend
        for i in range(30):
            if check_backend():
                print("✅ Backend started successfully")
                return True
            time.sleep(1)
        
        print("❌ Backend failed to start")
        return False
        
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        return False

def setup_test_environment():
    """Set up test environment with users and chat rooms."""
    print("🔧 Setting up test environment...")
    
    # Create test users
    test_users = [
        {"username": "alice", "email": "<EMAIL>", "password": "test123", "full_name": "Alice Johnson"},
        {"username": "bob", "email": "<EMAIL>", "password": "test123", "full_name": "Bob Smith"},
        {"username": "charlie", "email": "<EMAIL>", "password": "test123", "full_name": "Charlie Brown"}
    ]
    
    created_users = []
    for user_data in test_users:
        try:
            response = requests.post("http://localhost:8001/auth/register", json=user_data)
            if response.status_code == 200:
                print(f"✅ Created user: {user_data['username']}")
                created_users.append(user_data)
            else:
                print(f"⚠️ User {user_data['username']} might already exist")
                created_users.append(user_data)
        except Exception as e:
            print(f"❌ Error creating user {user_data['username']}: {e}")
    
    # Set up general chat room
    try:
        backend_dir = os.path.join(os.path.dirname(__file__), "backend")
        result = subprocess.run(
            ["python", "setup_general_chat.py"],
            cwd=backend_dir,
            capture_output=True,
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print("✅ General chat room setup completed")
        else:
            print("⚠️ General chat setup had issues")
    except Exception as e:
        print(f"❌ Error setting up general chat: {e}")
    
    return created_users

def test_api_endpoints():
    """Test that all required API endpoints work."""
    print("🧪 Testing API endpoints...")
    
    try:
        # Test login
        response = requests.post("http://localhost:8001/auth/login", json={
            "username": "alice",
            "password": "test123"
        })
        
        if response.status_code == 200:
            token = response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ Login API working")
            
            # Test get chat rooms
            rooms_response = requests.get("http://localhost:8001/chat-rooms", headers=headers)
            if rooms_response.status_code == 200:
                rooms = rooms_response.json()
                print(f"✅ Chat rooms API working ({len(rooms)} rooms found)")
                
                # Test sending a message if rooms exist
                if rooms:
                    room_id = rooms[0]['id']
                    message_response = requests.post(
                        f"http://localhost:8001/chat-rooms/{room_id}/messages",
                        json={"content": "Test message from API test"},
                        headers=headers
                    )
                    if message_response.status_code == 200:
                        print("✅ Send message API working")
                    else:
                        print(f"❌ Send message API failed: {message_response.status_code}")
                        print(f"Response: {message_response.text}")
                
                # Test getting messages
                if rooms:
                    room_id = rooms[0]['id']
                    messages_response = requests.get(
                        f"http://localhost:8001/chat-rooms/{room_id}/messages",
                        headers=headers
                    )
                    if messages_response.status_code == 200:
                        messages = messages_response.json()
                        print(f"✅ Get messages API working ({len(messages)} messages found)")
                    else:
                        print(f"❌ Get messages API failed: {messages_response.status_code}")
            else:
                print(f"❌ Chat rooms API failed: {rooms_response.status_code}")
                print(f"Response: {rooms_response.text}")
            
            # Test get users
            users_response = requests.get("http://localhost:8001/users", headers=headers)
            if users_response.status_code == 200:
                users = users_response.json()
                print(f"✅ Users API working ({len(users)} users found)")
            else:
                print(f"❌ Users API failed: {users_response.status_code}")
                
        else:
            print(f"❌ Login API failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ API test error: {e}")

def show_test_instructions():
    """Show detailed testing instructions."""
    print("\n" + "=" * 70)
    print("🎯 WORKING CHAT APPLICATION - TESTING INSTRUCTIONS")
    print("=" * 70)
    print("\n📋 WHAT TO TEST:")
    print("1. Login with: alice / test123")
    print("2. You should see:")
    print("   ✅ Modern WhatsApp-like interface with green theme")
    print("   ✅ Sidebar with chat list on the left")
    print("   ✅ Chat area on the right")
    print("   ✅ 'General Chat' room in the sidebar")
    print("   ✅ Other users (bob, charlie) in the sidebar")
    print("\n🎯 CRITICAL TESTS:")
    print("3. Click on 'General Chat' in the sidebar")
    print("   ✅ Chat header should update to show 'General Chat'")
    print("   ✅ Messages area should load (may show 'No messages yet')")
    print("   ✅ Message input should be enabled at the bottom")
    print("\n4. Type a message and press Enter")
    print("   ✅ Message should appear in a green bubble on the right")
    print("   ✅ Message should actually send (check console for confirmation)")
    print("   ✅ No 'coming soon' or error messages")
    print("\n5. Open a second client instance:")
    print("   • Run the client again in another window")
    print("   • Login with: bob / test123")
    print("   • Click on 'General Chat'")
    print("   • You should see alice's message")
    print("   • Send a message from bob")
    print("   • Alice should see bob's message in real-time")
    print("\n🔍 WHAT SHOULD WORK (NO BROKEN FEATURES):")
    print("   ✅ Chat room selection (clicking actually works)")
    print("   ✅ Message sending (messages actually send)")
    print("   ✅ Message receiving (real-time delivery)")
    print("   ✅ User interface updates properly")
    print("   ✅ No 'coming soon' placeholders")
    print("   ✅ All buttons and menus functional")
    print("\n❌ IF SOMETHING DOESN'T WORK:")
    print("   • Check the console for error messages")
    print("   • Make sure backend is running on port 8001")
    print("   • Try refreshing by restarting the client")
    print("\n" + "=" * 70)

def start_client():
    """Start the working chat client."""
    print("🖥️ Starting WORKING chat client...")
    
    client_dir = os.path.join(os.path.dirname(__file__), "client")
    
    try:
        subprocess.run(["python", "main.py"], cwd=client_dir)
    except KeyboardInterrupt:
        print("\n👋 Client testing completed")
    except Exception as e:
        print(f"❌ Client error: {e}")

def main():
    """Main test function."""
    print("💬 WORKING CHAT APPLICATION TEST")
    print("=" * 50)
    print("🎯 Goal: Verify chat room selection and messaging ACTUALLY WORKS")
    print("=" * 50)
    
    # Start backend
    if not start_backend():
        print("❌ Cannot proceed without backend")
        return
    
    # Setup test environment
    setup_test_environment()
    
    # Test API endpoints
    test_api_endpoints()
    
    # Show instructions
    show_test_instructions()
    
    # Ask to start client
    choice = input("\nStart the WORKING chat client? (y/n): ").strip().lower()
    
    if choice == 'y':
        start_client()
    else:
        print("\n👋 Setup complete! Start client manually:")
        print("cd client && python main.py")
        print("\nTest accounts:")
        print("• alice / test123")
        print("• bob / test123")
        print("• charlie / test123")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Test interrupted")
    except Exception as e:
        print(f"❌ Test error: {e}")
