#!/usr/bin/env python3
"""
Test the fully functional WhatsApp-like chat application.
This verifies that all features actually work, not just show "coming soon".
"""

import os
import sys
import subprocess
import time
import requests
import threading

def check_backend():
    """Check if backend is running."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=3)
        return response.status_code == 200
    except:
        return False

def start_backend_if_needed():
    """Start backend if not running."""
    if check_backend():
        print("✅ Backend is already running")
        return True
    
    print("🚀 Starting backend server...")
    backend_dir = os.path.join(os.path.dirname(__file__), "backend")
    
    try:
        if os.name == 'nt':  # Windows
            subprocess.Popen(
                ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"],
                cwd=backend_dir,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
        else:
            subprocess.Popen(
                ["python3", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"],
                cwd=backend_dir
            )
        
        # Wait for backend to start
        for i in range(30):
            if check_backend():
                print("✅ Backend started successfully")
                return True
            time.sleep(1)
        
        print("❌ Backend failed to start")
        return False
        
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        return False

def setup_test_data():
    """Set up test data for functional testing."""
    print("📊 Setting up test data...")
    
    # Create test users
    test_users = [
        {"username": "alice", "email": "<EMAIL>", "password": "test123", "full_name": "Alice Johnson"},
        {"username": "bob", "email": "<EMAIL>", "password": "test123", "full_name": "Bob Smith"}
    ]
    
    for user_data in test_users:
        try:
            response = requests.post("http://localhost:8001/auth/register", json=user_data)
            if response.status_code == 200:
                print(f"✅ Created user: {user_data['username']}")
            else:
                print(f"⚠️ User {user_data['username']} might already exist")
        except Exception as e:
            print(f"❌ Error creating user {user_data['username']}: {e}")
    
    # Set up general chat
    try:
        backend_dir = os.path.join(os.path.dirname(__file__), "backend")
        result = subprocess.run(
            ["python", "setup_general_chat.py"],
            cwd=backend_dir,
            capture_output=True,
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print("✅ General chat setup completed")
        else:
            print("⚠️ General chat setup had issues")
    except Exception as e:
        print(f"❌ Error setting up general chat: {e}")

def test_api_functionality():
    """Test that API endpoints work correctly."""
    print("🧪 Testing API functionality...")
    
    try:
        # Test login
        response = requests.post("http://localhost:8001/auth/login", json={
            "username": "alice",
            "password": "test123"
        })
        
        if response.status_code == 200:
            token = response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ Login API working")
            
            # Test chat rooms
            rooms_response = requests.get("http://localhost:8001/chat-rooms", headers=headers)
            if rooms_response.status_code == 200:
                rooms = rooms_response.json()
                print(f"✅ Chat rooms API working ({len(rooms)} rooms)")
                
                if rooms:
                    # Test sending message
                    room_id = rooms[0]['id']
                    message_response = requests.post(
                        f"http://localhost:8001/chat-rooms/{room_id}/messages",
                        json={"content": "Test message from API"},
                        headers=headers
                    )
                    if message_response.status_code == 200:
                        print("✅ Send message API working")
                    else:
                        print(f"❌ Send message API failed: {message_response.status_code}")
            else:
                print(f"❌ Chat rooms API failed: {rooms_response.status_code}")
            
            # Test users list
            users_response = requests.get("http://localhost:8001/users", headers=headers)
            if users_response.status_code == 200:
                users = users_response.json()
                print(f"✅ Users API working ({len(users)} users)")
            else:
                print(f"❌ Users API failed: {users_response.status_code}")
                
        else:
            print(f"❌ Login API failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API test error: {e}")

def show_test_instructions():
    """Show testing instructions."""
    print("\n" + "=" * 60)
    print("🎯 FUNCTIONAL CHAT APPLICATION TEST")
    print("=" * 60)
    print("\n📱 WHAT TO TEST:")
    print("1. Login with: alice / test123")
    print("2. Look for the modern WhatsApp-like interface")
    print("3. Click on 'General Chat' to join")
    print("4. Type a message and press Enter - IT SHOULD ACTUALLY SEND!")
    print("5. Try these WORKING features:")
    print("   • Right-click on messages for context menu")
    print("   • Click the emoji button (😊) to add emojis")
    print("   • Click the attachment button (📎) for file options")
    print("   • Click the menu button (⋮) for settings")
    print("   • Search for chats in the search bar")
    print("\n🔍 WHAT SHOULD WORK (NO MORE 'COMING SOON'):")
    print("   ✅ Sending and receiving messages")
    print("   ✅ Message status indicators (✓, ✓✓)")
    print("   ✅ Emoji picker")
    print("   ✅ File attachments")
    print("   ✅ Profile settings")
    print("   ✅ Chat search")
    print("   ✅ Message reactions")
    print("   ✅ Reply to messages")
    print("   ✅ Dark/light theme toggle")
    print("\n🌐 FOR SECOND USER:")
    print("   • Open another client instance")
    print("   • Login with: bob / test123")
    print("   • Join the same 'General Chat'")
    print("   • Messages should appear in real-time!")
    print("\n" + "=" * 60)

def start_client():
    """Start the client application."""
    print("🖥️ Starting fully functional chat client...")
    
    client_dir = os.path.join(os.path.dirname(__file__), "client")
    
    try:
        subprocess.run(["python", "main.py"], cwd=client_dir)
    except KeyboardInterrupt:
        print("\n👋 Client testing completed")
    except Exception as e:
        print(f"❌ Client error: {e}")

def main():
    """Main test function."""
    print("💬 FULLY FUNCTIONAL WHATSAPP-LIKE CHAT TEST")
    print("=" * 50)
    
    # Start backend if needed
    if not start_backend_if_needed():
        print("❌ Cannot proceed without backend")
        return
    
    # Setup test data
    setup_test_data()
    
    # Test API functionality
    test_api_functionality()
    
    # Show instructions
    show_test_instructions()
    
    # Ask to start client
    choice = input("\nStart the FULLY FUNCTIONAL client? (y/n): ").strip().lower()
    
    if choice == 'y':
        start_client()
    else:
        print("\n👋 Setup complete! Start client manually:")
        print("cd client && python main.py")
        print("\nRemember: Login with alice/test123 or bob/test123")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Test interrupted")
    except Exception as e:
        print(f"❌ Test error: {e}")
