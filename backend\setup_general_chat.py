#!/usr/bin/env python3
"""
Simple script to create a General Chat room and add all users to it.
This ensures users can connect and chat with each other.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import get_db, init_db
from app.users import Chat<PERSON>oom, ChatMember, User
from sqlalchemy import select

async def setup_general_chat():
    """Create General Chat room and add all users."""
    print("🏠 Setting up General Chat room...")
    
    # Initialize database
    await init_db()
    
    # Get database session
    async for db in get_db():
        try:
            # Check if General Chat already exists
            result = await db.execute(select(ChatRoom).where(ChatRoom.name == "General Chat"))
            general_room = result.scalar_one_or_none()
            
            if not general_room:
                # Create General Chat room
                general_room = ChatRoom(
                    name="General Chat",
                    description="Welcome! Main chat room for everyone to connect and chat.",
                    is_group=True,
                    created_by=1  # Use first user as creator
                )
                
                db.add(general_room)
                await db.flush()  # Get the room ID
                print(f"✅ Created General Chat room (ID: {general_room.id})")
            else:
                print(f"✅ General Chat room already exists (ID: {general_room.id})")
            
            # Get all users
            result = await db.execute(select(User))
            users = result.scalars().all()
            
            if not users:
                print("❌ No users found in database")
                return
            
            # Get existing members
            result = await db.execute(
                select(ChatMember).where(ChatMember.chat_room_id == general_room.id)
            )
            existing_members = result.scalars().all()
            existing_user_ids = {member.user_id for member in existing_members}
            
            # Add users who aren't already members
            added_count = 0
            for user in users:
                if user.id not in existing_user_ids:
                    member = ChatMember(
                        chat_room_id=general_room.id,
                        user_id=user.id,
                        role="member"
                    )
                    db.add(member)
                    added_count += 1
                    print(f"✅ Added {user.username} to General Chat")
            
            if added_count == 0:
                print("✅ All users are already in General Chat")
            
            await db.commit()
            
            print(f"\n🎉 General Chat setup complete!")
            print(f"👥 Total users in General Chat: {len(users)}")
            print(f"🆕 Newly added users: {added_count}")
            
        except Exception as e:
            print(f"❌ Error setting up General Chat: {e}")
            await db.rollback()
        finally:
            await db.close()
            break

async def list_current_state():
    """List current rooms and users."""
    print("\n📋 Current Chat Application State:")
    print("=" * 50)
    
    async for db in get_db():
        try:
            # List users
            result = await db.execute(select(User))
            users = result.scalars().all()
            
            print(f"\n👥 Users ({len(users)}):")
            for user in users:
                status = "🟢 Online" if user.is_online else "⚫ Offline"
                print(f"  • {user.username} (ID: {user.id}) - {status}")
            
            # List rooms
            result = await db.execute(select(ChatRoom))
            rooms = result.scalars().all()
            
            print(f"\n🏠 Chat Rooms ({len(rooms)}):")
            for room in rooms:
                # Get member count
                result = await db.execute(
                    select(ChatMember).where(ChatMember.chat_room_id == room.id)
                )
                members = result.scalars().all()
                
                room_type = "🏢 Group" if room.is_group else "💬 Private"
                print(f"  • {room.name} (ID: {room.id}) - {room_type}")
                print(f"    Description: {room.description or 'No description'}")
                print(f"    Members: {len(members)}")
                
                if len(members) <= 5:  # Show member names for small rooms
                    member_result = await db.execute(
                        select(User.username)
                        .join(ChatMember, User.id == ChatMember.user_id)
                        .where(ChatMember.chat_room_id == room.id)
                    )
                    member_names = [name for name, in member_result.all()]
                    print(f"    Member names: {', '.join(member_names)}")
                print()
            
        except Exception as e:
            print(f"❌ Error listing state: {e}")
        finally:
            await db.close()
            break

def main():
    """Main function."""
    print("💬 Chat Application - General Chat Setup")
    print("=" * 50)
    
    try:
        # Setup General Chat
        asyncio.run(setup_general_chat())
        
        # Show current state
        asyncio.run(list_current_state())
        
        print("=" * 50)
        print("🎉 Setup complete!")
        print("\n📱 How to connect users:")
        print("1. Start the chat application")
        print("2. Login with any user account")
        print("3. Look for 'General Chat' in the chat list")
        print("4. Click on it to join and start chatting!")
        print("\n💡 Users can also create private chats by clicking on other users")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")

if __name__ == "__main__":
    main()
