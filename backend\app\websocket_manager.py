import json
import asyncio
from typing import Dict, List, Set
from datetime import datetime, timedelta
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession
from .database import AsyncSessionLocal
from .auth import verify_token
from .users import User
from .messages import WSMessage, WSMessageType, WSTypingMessage, WSPresenceMessage, WSErrorMessage
from sqlalchemy import select

class ConnectionManager:
    def __init__(self):
        # Store active connections: {user_id: {websocket, chat_rooms}}
        self.active_connections: Dict[int, Dict] = {}
        # Store room memberships: {room_id: {user_ids}}
        self.room_members: Dict[int, Set[int]] = {}
        # Store typing indicators: {room_id: {user_id: expires_at}}
        self.typing_indicators: Dict[int, Dict[int, datetime]] = {}

    async def connect(self, websocket: WebSocket, user_id: int):
        """Accept a WebSocket connection and register the user."""
        await websocket.accept()
        
        # Store connection info
        self.active_connections[user_id] = {
            "websocket": websocket,
            "chat_rooms": set(),
            "connected_at": datetime.utcnow()
        }
        
        # Update user online status in database
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(User).where(User.id == user_id))
            user = result.scalar_one_or_none()
            if user:
                user.is_online = True
                await db.commit()
        
        print(f"User {user_id} connected via WebSocket")

    async def disconnect(self, user_id: int):
        """Disconnect a user and clean up."""
        if user_id in self.active_connections:
            connection_info = self.active_connections[user_id]
            
            # Remove from all chat rooms
            for room_id in connection_info["chat_rooms"]:
                if room_id in self.room_members:
                    self.room_members[room_id].discard(user_id)
                    if not self.room_members[room_id]:
                        del self.room_members[room_id]
            
            # Remove typing indicators
            for room_id in list(self.typing_indicators.keys()):
                if user_id in self.typing_indicators[room_id]:
                    del self.typing_indicators[room_id][user_id]
                    if not self.typing_indicators[room_id]:
                        del self.typing_indicators[room_id]
            
            # Remove connection
            del self.active_connections[user_id]
            
            # Update user offline status in database
            async with AsyncSessionLocal() as db:
                result = await db.execute(select(User).where(User.id == user_id))
                user = result.scalar_one_or_none()
                if user:
                    user.is_online = False
                    user.last_seen = datetime.utcnow()
                    await db.commit()
            
            print(f"User {user_id} disconnected from WebSocket")

    async def join_room(self, user_id: int, room_id: int):
        """Add user to a chat room."""
        if user_id in self.active_connections:
            self.active_connections[user_id]["chat_rooms"].add(room_id)
            
            if room_id not in self.room_members:
                self.room_members[room_id] = set()
            self.room_members[room_id].add(user_id)
            
            # Notify other room members that user joined
            await self.broadcast_to_room(room_id, {
                "type": WSMessageType.USER_JOINED,
                "user_id": user_id,
                "timestamp": datetime.utcnow().isoformat()
            }, exclude_user=user_id)

    async def leave_room(self, user_id: int, room_id: int):
        """Remove user from a chat room."""
        if user_id in self.active_connections:
            self.active_connections[user_id]["chat_rooms"].discard(room_id)
            
            if room_id in self.room_members:
                self.room_members[room_id].discard(user_id)
                if not self.room_members[room_id]:
                    del self.room_members[room_id]
            
            # Remove typing indicator if exists
            if room_id in self.typing_indicators and user_id in self.typing_indicators[room_id]:
                del self.typing_indicators[room_id][user_id]
                if not self.typing_indicators[room_id]:
                    del self.typing_indicators[room_id]
            
            # Notify other room members that user left
            await self.broadcast_to_room(room_id, {
                "type": WSMessageType.USER_LEFT,
                "user_id": user_id,
                "timestamp": datetime.utcnow().isoformat()
            }, exclude_user=user_id)

    async def send_personal_message(self, user_id: int, message: dict):
        """Send a message to a specific user."""
        if user_id in self.active_connections:
            websocket = self.active_connections[user_id]["websocket"]
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                print(f"Error sending message to user {user_id}: {e}")
                await self.disconnect(user_id)

    async def broadcast_to_room(self, room_id: int, message: dict, exclude_user: int = None):
        """Broadcast a message to all users in a chat room."""
        if room_id in self.room_members:
            for user_id in self.room_members[room_id]:
                if exclude_user and user_id == exclude_user:
                    continue
                await self.send_personal_message(user_id, message)

    async def handle_typing_indicator(self, user_id: int, room_id: int, is_typing: bool):
        """Handle typing indicators."""
        if is_typing:
            # Add typing indicator with expiration
            if room_id not in self.typing_indicators:
                self.typing_indicators[room_id] = {}
            
            self.typing_indicators[room_id][user_id] = datetime.utcnow() + timedelta(seconds=10)
            
            # Broadcast typing start to room members
            await self.broadcast_to_room(room_id, {
                "type": WSMessageType.TYPING_START,
                "user_id": user_id,
                "room_id": room_id,
                "timestamp": datetime.utcnow().isoformat()
            }, exclude_user=user_id)
        else:
            # Remove typing indicator
            if room_id in self.typing_indicators and user_id in self.typing_indicators[room_id]:
                del self.typing_indicators[room_id][user_id]
                if not self.typing_indicators[room_id]:
                    del self.typing_indicators[room_id]
                
                # Broadcast typing stop to room members
                await self.broadcast_to_room(room_id, {
                    "type": WSMessageType.TYPING_STOP,
                    "user_id": user_id,
                    "room_id": room_id,
                    "timestamp": datetime.utcnow().isoformat()
                }, exclude_user=user_id)

    async def cleanup_expired_typing_indicators(self):
        """Remove expired typing indicators."""
        current_time = datetime.utcnow()
        expired_indicators = []
        
        for room_id, indicators in self.typing_indicators.items():
            for user_id, expires_at in indicators.items():
                if current_time > expires_at:
                    expired_indicators.append((room_id, user_id))
        
        for room_id, user_id in expired_indicators:
            await self.handle_typing_indicator(user_id, room_id, False)

    def get_online_users(self) -> List[int]:
        """Get list of online user IDs."""
        return list(self.active_connections.keys())

    def get_room_online_users(self, room_id: int) -> List[int]:
        """Get list of online users in a specific room."""
        if room_id in self.room_members:
            return [user_id for user_id in self.room_members[room_id] 
                   if user_id in self.active_connections]
        return []

    async def broadcast_user_presence(self, user_id: int, is_online: bool):
        """Broadcast user presence status to all relevant rooms."""
        if user_id in self.active_connections:
            user_rooms = self.active_connections[user_id]["chat_rooms"]
            for room_id in user_rooms:
                await self.broadcast_to_room(room_id, {
                    "type": WSMessageType.USER_ONLINE if is_online else WSMessageType.USER_OFFLINE,
                    "user_id": user_id,
                    "timestamp": datetime.utcnow().isoformat()
                }, exclude_user=user_id)

# Global connection manager instance
manager = ConnectionManager()

# Background task to clean up expired typing indicators
async def cleanup_typing_indicators():
    """Background task to clean up expired typing indicators."""
    while True:
        await manager.cleanup_expired_typing_indicators()
        await asyncio.sleep(5)  # Check every 5 seconds

# Start the cleanup task
asyncio.create_task(cleanup_typing_indicators())
