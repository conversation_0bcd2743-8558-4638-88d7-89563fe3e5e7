#!/usr/bin/env python3
"""
Complete login/register functionality fix script.
This script ensures all components are working correctly.
"""

import os
import sys
import subprocess
import time
import requests
import threading

def check_backend_running():
    """Check if backend is running."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """Start the backend server."""
    print("🚀 Starting backend server...")
    
    backend_dir = os.path.join(os.path.dirname(__file__), "backend")
    
    if os.name == 'nt':  # Windows
        cmd = ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"]
    else:  # Unix/Linux/Mac
        cmd = ["python3", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"]
    
    process = subprocess.Popen(cmd, cwd=backend_dir)
    
    # Wait for server to start
    print("⏳ Waiting for backend to start...")
    for i in range(30):  # Wait up to 30 seconds
        if check_backend_running():
            print("✅ Backend server is running!")
            return process
        time.sleep(1)
    
    print("❌ Backend failed to start")
    return None

def test_authentication():
    """Test authentication functionality."""
    print("\n🧪 Testing Authentication...")
    
    # Test registration
    try:
        response = requests.post("http://localhost:8001/auth/register", json={
            "username": "testuser999",
            "email": "<EMAIL>",
            "password": "testpass123",
            "full_name": "Test User 999"
        })
        
        if response.status_code == 200:
            print("✅ Registration working")
        else:
            print(f"⚠️ Registration response: {response.status_code}")
    except Exception as e:
        print(f"❌ Registration error: {e}")
    
    # Test login
    try:
        response = requests.post("http://localhost:8001/auth/login", json={
            "username": "testuser999",
            "password": "testpass123"
        })
        
        if response.status_code == 200:
            print("✅ Login working")
            token_data = response.json()
            
            # Test authenticated endpoint
            headers = {"Authorization": f"Bearer {token_data['access_token']}"}
            response = requests.get("http://localhost:8001/users/me", headers=headers)
            
            if response.status_code == 200:
                print("✅ Authenticated endpoints working")
            else:
                print(f"⚠️ Auth endpoint response: {response.status_code}")
                
        else:
            print(f"❌ Login failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Login error: {e}")

def start_client():
    """Start the client application."""
    print("\n🖥️ Starting client application...")
    
    client_dir = os.path.join(os.path.dirname(__file__), "client")
    
    if os.name == 'nt':  # Windows
        cmd = ["python", "main.py"]
    else:  # Unix/Linux/Mac
        cmd = ["python3", "main.py"]
    
    try:
        subprocess.run(cmd, cwd=client_dir)
    except KeyboardInterrupt:
        print("\n👋 Client application closed")
    except Exception as e:
        print(f"❌ Client error: {e}")

def main():
    """Main function."""
    print("🔧 Chat Application Login Fix")
    print("=" * 50)
    
    # Check if backend is already running
    if check_backend_running():
        print("✅ Backend is already running")
    else:
        # Start backend
        backend_process = start_backend()
        if not backend_process:
            print("❌ Failed to start backend. Please check the logs.")
            return
    
    # Test authentication
    test_authentication()
    
    # Ask user if they want to start the client
    print("\n" + "=" * 50)
    print("🎉 Login functionality is working!")
    print("\nOptions:")
    print("1. Start the client application")
    print("2. Exit")
    
    choice = input("\nEnter your choice (1 or 2): ").strip()
    
    if choice == "1":
        start_client()
    else:
        print("👋 Goodbye!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
