import asyncio
import json
import websockets
import threading
from typing import Callable, Optional, Dict, Any
from datetime import datetime
import os

class WebSocketClient:
    """WebSocket client for real-time communication."""
    
    def __init__(self, token: str, on_message: Callable[[Dict[str, Any]], None]):
        self.token = token
        self.on_message = on_message
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.is_connected = False
        self.is_connecting = False
        self.should_reconnect = True
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        
        # WebSocket URL
        base_url = os.getenv("API_BASE_URL", "http://localhost:8001")
        ws_url = base_url.replace("http://", "ws://").replace("https://", "wss://")
        self.ws_url = f"{ws_url}/ws/chat?token={self.token}"
        
        # Event loop and thread
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.thread: Optional[threading.Thread] = None
        
        # Typing indicator state
        self.typing_rooms: set = set()
    
    def start(self):
        """Start the WebSocket connection in a background thread."""
        if self.thread and self.thread.is_alive():
            return
        
        self.should_reconnect = True
        self.thread = threading.Thread(target=self._run_event_loop, daemon=True)
        self.thread.start()
    
    def stop(self):
        """Stop the WebSocket connection."""
        self.should_reconnect = False
        
        if self.loop and not self.loop.is_closed():
            asyncio.run_coroutine_threadsafe(self._disconnect(), self.loop)
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
    
    def _run_event_loop(self):
        """Run the asyncio event loop in the background thread."""
        try:
            # Create new event loop for this thread
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            # Run the connection loop
            self.loop.run_until_complete(self._connection_loop())
        except Exception as e:
            print(f"WebSocket event loop error: {e}")
        finally:
            if self.loop and not self.loop.is_closed():
                self.loop.close()
    
    async def _connection_loop(self):
        """Main connection loop with reconnection logic."""
        while self.should_reconnect:
            try:
                await self._connect_and_listen()
            except Exception as e:
                print(f"WebSocket connection error: {e}")
                
                if self.should_reconnect:
                    self.reconnect_attempts += 1
                    if self.reconnect_attempts <= self.max_reconnect_attempts:
                        wait_time = min(2 ** self.reconnect_attempts, 30)  # Exponential backoff
                        print(f"Reconnecting in {wait_time} seconds... (attempt {self.reconnect_attempts})")
                        await asyncio.sleep(wait_time)
                    else:
                        print("Max reconnection attempts reached")
                        break
                else:
                    break
    
    async def _connect_and_listen(self):
        """Connect to WebSocket and listen for messages."""
        self.is_connecting = True
        
        try:
            async with websockets.connect(
                self.ws_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            ) as websocket:
                self.websocket = websocket
                self.is_connected = True
                self.is_connecting = False
                self.reconnect_attempts = 0
                
                print("WebSocket connected successfully")
                
                # Send ping periodically to keep connection alive
                ping_task = asyncio.create_task(self._ping_loop())
                
                try:
                    # Listen for messages
                    async for message in websocket:
                        try:
                            data = json.loads(message)
                            if data.get("type") == "pong":
                                # Received pong response from server
                                continue
                            self._handle_message(data)
                        except json.JSONDecodeError:
                            print(f"Invalid JSON received: {message}")
                        except Exception as e:
                            print(f"Error handling message: {e}")
                
                except websockets.exceptions.ConnectionClosed:
                    print("WebSocket connection closed")
                except Exception as e:
                    print(f"WebSocket error: {e}")
                finally:
                    ping_task.cancel()
                    if not ping_task.done():
                        ping_task.cancel()
                    
        except Exception as e:
            print(f"WebSocket connection failed: {e}")
            raise
        finally:
            self.is_connected = False
            self.is_connecting = False
            self.websocket = None
    
    async def _ping_loop(self):
        """Send periodic ping messages to keep connection alive."""
        try:
            while self.is_connected and self.should_reconnect:
                await asyncio.sleep(30)  # Send ping every 30 seconds
                
                # Double-check connection state before sending
                if not (self.is_connected and self.websocket and not self.websocket.closed):
                    break
                    
                try:
                    await self.send_message({"type": "ping"})
                    # Wait briefly to ensure send completes
                    await asyncio.sleep(0.1)
                except (websockets.exceptions.ConnectionClosed, 
                       websockets.exceptions.WebSocketException) as e:
                    print(f"Ping failed: {e}")
                    break
                except Exception as e:
                    print(f"Unexpected ping error: {e}")
                    continue  # Continue loop for non-fatal errors
        except asyncio.CancelledError:
            pass
        except Exception as e:
            print(f"Ping loop error: {e}")
            raise
    
    def _handle_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket message."""
        try:
            # Call the message handler on the main thread
            if self.on_message:
                self.on_message(data)
        except Exception as e:
            print(f"Error in message handler: {e}")
    
    async def _disconnect(self):
        """Disconnect from WebSocket."""
        self.is_connected = False
        self.should_reconnect = False
        
        if self.websocket:
            try:
                await self.websocket.close()
            except Exception as e:
                print(f"Error closing WebSocket: {e}")
    
    def send_message(self, message: Dict[str, Any]):
        """Send a message through WebSocket."""
        if not self.is_connected or not self.websocket or not self.loop:
            print("WebSocket not connected, cannot send message")
            return
        
        try:
            # Schedule the send operation on the WebSocket thread
            future = asyncio.run_coroutine_threadsafe(
                self._send_message_async(message), 
                self.loop
            )
            # Don't wait for the result to avoid blocking the UI
        except Exception as e:
            print(f"Error sending message: {e}")
    
    async def _send_message_async(self, message: Dict[str, Any]):
        """Send message asynchronously."""
        if self.websocket and self.is_connected:
            try:
                await self.websocket.send(json.dumps(message))
            except Exception as e:
                print(f"Error sending WebSocket message: {e}")
                raise
    
    def join_room(self, room_id: int):
        """Join a chat room."""
        self.send_message({
            "type": "join_room",
            "room_id": room_id
        })
    
    def leave_room(self, room_id: int):
        """Leave a chat room."""
        self.send_message({
            "type": "leave_room",
            "room_id": room_id
        })
    
    def start_typing(self, room_id: int):
        """Start typing indicator."""
        if room_id not in self.typing_rooms:
            self.typing_rooms.add(room_id)
            self.send_message({
                "type": "typing_start",
                "room_id": room_id
            })
    
    def stop_typing(self, room_id: int):
        """Stop typing indicator."""
        if room_id in self.typing_rooms:
            self.typing_rooms.discard(room_id)
            self.send_message({
                "type": "typing_stop",
                "room_id": room_id
            })
    
    def stop_all_typing(self):
        """Stop all typing indicators."""
        for room_id in list(self.typing_rooms):
            self.stop_typing(room_id)
    
    @property
    def connection_status(self) -> str:
        """Get current connection status."""
        if self.is_connected:
            return "Connected"
        elif self.is_connecting:
            return "Connecting..."
        elif self.should_reconnect and self.reconnect_attempts > 0:
            return f"Reconnecting... (attempt {self.reconnect_attempts})"
        else:
            return "Disconnected"
