import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
from PIL import Image, ImageTk, ImageDraw, ImageFont
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
import os
import tempfile

class StatusStoriesManager:
    """WhatsApp-like status and stories manager."""

    def __init__(self, parent, api_client, user_info: Dict[str, Any]):
        self.parent = parent
        self.api_client = api_client
        self.user_info = user_info

        # Status data
        self.status_updates: List[Dict[str, Any]] = []
        self.my_status_updates: List[Dict[str, Any]] = []

        # UI elements
        self.status_window = None
        self.status_viewer = None

        # Colors for text status
        self.status_colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ]

        self.load_status_updates()

    def load_status_updates(self):
        """Load status updates from API."""
        def load_thread():
            try:
                status_updates = self.api_client.get_status_updates()
                if status_updates:
                    self.parent.after(0, lambda: self.update_status_list(status_updates))
            except Exception as e:
                print(f"Error loading status updates: {e}")

        threading.Thread(target=load_thread, daemon=True).start()

    def update_status_list(self, status_updates: List[Dict[str, Any]]):
        """Update the status list."""
        self.status_updates = [s for s in status_updates if s['user_id'] != self.user_info['id']]
        self.my_status_updates = [s for s in status_updates if s['user_id'] == self.user_info['id']]

    def show_status_window(self):
        """Show the status management window."""
        if self.status_window:
            self.status_window.lift()
            return

        self.status_window = tk.Toplevel(self.parent)
        self.status_window.title("Status Updates")
        self.status_window.geometry("400x600")
        self.status_window.resizable(False, False)
        self.status_window.transient(self.parent)

        # Center window
        self.status_window.update_idletasks()
        x = (self.status_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.status_window.winfo_screenheight() // 2) - (600 // 2)
        self.status_window.geometry(f"400x600+{x}+{y}")

        self.status_window.protocol("WM_DELETE_WINDOW", self.close_status_window)

        self.setup_status_ui()

    def setup_status_ui(self):
        """Set up the status UI."""
        main_frame = ttk.Frame(self.status_window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        header_frame.columnconfigure(1, weight=1)

        ttk.Label(header_frame, text="Status Updates",
                 font=("Arial", 16, "bold")).grid(row=0, column=0, sticky=tk.W)

        # Add status button
        add_btn = ttk.Button(header_frame, text="+ Add Status",
                            command=self.show_add_status_dialog)
        add_btn.grid(row=0, column=1, sticky=tk.E)

        # My Status section
        my_status_frame = ttk.LabelFrame(main_frame, text="My Status", padding="10")
        my_status_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        my_status_frame.columnconfigure(0, weight=1)

        if self.my_status_updates:
            self.create_my_status_display(my_status_frame)
        else:
            ttk.Label(my_status_frame, text="No status updates",
                     foreground="gray").grid(row=0, column=0)

        # Recent Updates section
        recent_frame = ttk.LabelFrame(main_frame, text="Recent Updates", padding="10")
        recent_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        recent_frame.columnconfigure(0, weight=1)
        recent_frame.rowconfigure(0, weight=1)

        # Scrollable list of status updates
        self.create_status_list(recent_frame)

        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)

    def create_my_status_display(self, parent):
        """Create display for user's own status updates."""
        for i, status in enumerate(self.my_status_updates[:3]):  # Show max 3
            status_item = self.create_status_item(parent, status, is_own=True)
            status_item.grid(row=i, column=0, sticky=(tk.W, tk.E), pady=2)

    def create_status_list(self, parent):
        """Create scrollable list of status updates."""
        # Canvas and scrollbar for scrolling
        canvas = tk.Canvas(parent, highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Add status items
        for i, status in enumerate(self.status_updates):
            status_item = self.create_status_item(scrollable_frame, status)
            status_item.grid(row=i, column=0, sticky=(tk.W, tk.E), pady=2)

        scrollable_frame.columnconfigure(0, weight=1)

    def create_status_item(self, parent, status: Dict[str, Any], is_own: bool = False):
        """Create a status item widget."""
        item_frame = ttk.Frame(parent, relief="solid", borderwidth=1, padding="10")
        item_frame.columnconfigure(1, weight=1)

        # User avatar (placeholder)
        avatar_frame = tk.Frame(item_frame, width=40, height=40, bg="#4ECDC4")
        avatar_frame.grid(row=0, column=0, rowspan=2, padx=(0, 10))
        avatar_frame.grid_propagate(False)

        # User initial
        initial = status.get('username', 'U')[0].upper()
        avatar_label = tk.Label(avatar_frame, text=initial, bg="#4ECDC4",
                               fg="white", font=("Arial", 12, "bold"))
        avatar_label.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

        # User name and time
        name_label = ttk.Label(item_frame, text=status.get('username', 'Unknown'),
                              font=("Arial", 10, "bold"))
        name_label.grid(row=0, column=1, sticky=tk.W)

        # Time ago
        created_at = datetime.fromisoformat(status['created_at'].replace('Z', '+00:00'))
        time_ago = self.get_time_ago(created_at)
        time_label = ttk.Label(item_frame, text=time_ago, foreground="gray",
                              font=("Arial", 8))
        time_label.grid(row=1, column=1, sticky=tk.W)

        # Status preview
        preview_text = self.get_status_preview(status)
        preview_label = ttk.Label(item_frame, text=preview_text, foreground="gray")
        preview_label.grid(row=0, column=2, rowspan=2, padx=(10, 0))

        # View count (for own status)
        if is_own and status.get('view_count', 0) > 0:
            views_label = ttk.Label(item_frame, text=f"👁 {status['view_count']}",
                                   foreground="blue", font=("Arial", 8))
            views_label.grid(row=1, column=2, sticky=tk.E, padx=(10, 0))

        # Click handler
        def on_click(event=None):
            self.view_status(status)

        item_frame.bind("<Button-1>", on_click)
        for child in item_frame.winfo_children():
            child.bind("<Button-1>", on_click)

        return item_frame

    def get_time_ago(self, timestamp: datetime) -> str:
        """Get human-readable time ago string."""
        now = datetime.now(timestamp.tzinfo)
        diff = now - timestamp

        if diff.days > 0:
            return f"{diff.days}d ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}h ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}m ago"
        else:
            return "Just now"

    def get_status_preview(self, status: Dict[str, Any]) -> str:
        """Get preview text for status."""
        if status.get('content'):
            content = status['content']
            return content[:20] + "..." if len(content) > 20 else content
        elif status.get('media_type') == 'image':
            return "📷 Photo"
        elif status.get('media_type') == 'video':
            return "🎥 Video"
        else:
            return "Status update"

    def show_add_status_dialog(self):
        """Show dialog to add new status."""
        dialog = AddStatusDialog(self.status_window, self.api_client,
                                self.status_colors, self.on_status_added)
        dialog.show()

    def on_status_added(self, status: Dict[str, Any]):
        """Handle new status added."""
        self.my_status_updates.insert(0, status)
        self.close_status_window()
        self.show_status_window()  # Refresh

    def view_status(self, status: Dict[str, Any]):
        """View a status update."""
        if self.status_viewer:
            self.status_viewer.destroy()

        self.status_viewer = StatusViewer(self.parent, status, self.api_client)
        self.status_viewer.show()

    def close_status_window(self):
        """Close the status window."""
        if self.status_window:
            self.status_window.destroy()
            self.status_window = None

class AddStatusDialog:
    """Dialog for adding new status updates."""

    def __init__(self, parent, api_client, colors: List[str], on_success: Callable):
        self.parent = parent
        self.api_client = api_client
        self.colors = colors
        self.on_success = on_success

        self.dialog = None
        self.selected_color = colors[0]
        self.selected_font = "Arial"
        self.media_file = None

    def show(self):
        """Show the add status dialog."""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Add Status Update")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")

        self.setup_dialog_ui()

    def setup_dialog_ui(self):
        """Set up the dialog UI."""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Notebook for different status types
        notebook = ttk.Notebook(main_frame)
        notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))

        # Text status tab
        text_frame = ttk.Frame(notebook, padding="20")
        notebook.add(text_frame, text="Text Status")
        self.setup_text_status_tab(text_frame)

        # Media status tab
        media_frame = ttk.Frame(notebook, padding="20")
        notebook.add(media_frame, text="Photo/Video")
        self.setup_media_status_tab(media_frame)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

        ttk.Button(button_frame, text="Cancel",
                  command=self.dialog.destroy).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="Post Status",
                  command=self.post_status).grid(row=0, column=1)

        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=1)

    def setup_text_status_tab(self, parent):
        """Set up text status tab."""
        # Text input
        ttk.Label(parent, text="Status Text:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        self.text_var = tk.StringVar()
        text_entry = tk.Text(parent, height=4, width=40, wrap=tk.WORD)
        text_entry.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        # Color selection
        ttk.Label(parent, text="Background Color:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))

        color_frame = ttk.Frame(parent)
        color_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        for i, color in enumerate(self.colors[:5]):  # Show first 5 colors
            color_btn = tk.Button(color_frame, bg=color, width=3, height=1,
                                 command=lambda c=color: self.select_color(c))
            color_btn.grid(row=0, column=i, padx=2)

        # Custom color button
        custom_btn = ttk.Button(color_frame, text="Custom",
                               command=self.choose_custom_color)
        custom_btn.grid(row=0, column=5, padx=(10, 0))

        # Font selection
        ttk.Label(parent, text="Font Style:").grid(row=4, column=0, sticky=tk.W, pady=(0, 5))

        self.font_var = tk.StringVar(value="Arial")
        font_combo = ttk.Combobox(parent, textvariable=self.font_var,
                                 values=["Arial", "Times New Roman", "Helvetica", "Comic Sans MS"])
        font_combo.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Store text widget reference
        self.text_widget = text_entry

    def setup_media_status_tab(self, parent):
        """Set up media status tab."""
        # File selection
        ttk.Label(parent, text="Select Photo or Video:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))

        file_frame = ttk.Frame(parent)
        file_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        file_frame.columnconfigure(0, weight=1)

        self.file_label = ttk.Label(file_frame, text="No file selected", foreground="gray")
        self.file_label.grid(row=0, column=0, sticky=tk.W)

        ttk.Button(file_frame, text="Browse",
                  command=self.select_media_file).grid(row=0, column=1, padx=(10, 0))

        # Caption
        ttk.Label(parent, text="Caption (optional):").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))

        self.caption_var = tk.StringVar()
        caption_entry = ttk.Entry(parent, textvariable=self.caption_var, width=40)
        caption_entry.grid(row=3, column=0, sticky=(tk.W, tk.E))

    def select_color(self, color: str):
        """Select background color."""
        self.selected_color = color

    def choose_custom_color(self):
        """Choose custom color."""
        color = colorchooser.askcolor(title="Choose Background Color")
        if color[1]:  # If color was selected
            self.selected_color = color[1]

    def select_media_file(self):
        """Select media file."""
        file_path = filedialog.askopenfilename(
            title="Select Photo or Video",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                ("Video files", "*.mp4 *.avi *.mov *.wmv"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.media_file = file_path
            filename = os.path.basename(file_path)
            self.file_label.config(text=filename, foreground="black")

    def post_status(self):
        """Post the status update."""
        # Determine status type and content
        current_tab = self.dialog.nametowidget(
            self.dialog.winfo_children()[0].winfo_children()[0].select()
        )

        if "Text Status" in str(current_tab):
            # Text status
            content = self.text_widget.get("1.0", tk.END).strip()
            if not content:
                messagebox.showwarning("Warning", "Please enter status text")
                return

            status_data = {
                'content': content,
                'background_color': self.selected_color,
                'font_style': self.font_var.get(),
                'privacy_setting': 'all'
            }
        else:
            # Media status
            if not self.media_file:
                messagebox.showwarning("Warning", "Please select a file")
                return

            # TODO: Upload media file first
            status_data = {
                'content': self.caption_var.get(),
                'media_url': self.media_file,  # This should be uploaded URL
                'media_type': 'image' if self.media_file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')) else 'video',
                'privacy_setting': 'all'
            }

        # Post status via API
        def post_thread():
            try:
                result = self.api_client.create_status_update(status_data)
                if result:
                    self.dialog.after(0, lambda: self.on_success(result))
                    self.dialog.after(0, self.dialog.destroy)
                else:
                    self.dialog.after(0, lambda: messagebox.showerror("Error", "Failed to post status"))
            except Exception as e:
                self.dialog.after(0, lambda: messagebox.showerror("Error", f"Error posting status: {e}"))

        threading.Thread(target=post_thread, daemon=True).start()

class StatusViewer:
    """Full-screen status viewer."""

    def __init__(self, parent, status: Dict[str, Any], api_client):
        self.parent = parent
        self.status = status
        self.api_client = api_client

        self.viewer_window = None
        self.progress_bar = None
        self.auto_close_timer = None

    def show(self):
        """Show the status viewer."""
        self.viewer_window = tk.Toplevel(self.parent)
        self.viewer_window.title("Status")
        self.viewer_window.attributes('-fullscreen', True)
        self.viewer_window.configure(bg='black')
        self.viewer_window.focus_set()

        # Bind escape key to close
        self.viewer_window.bind('<Escape>', lambda e: self.close_viewer())
        self.viewer_window.bind('<Button-1>', lambda e: self.close_viewer())

        self.setup_viewer_ui()

        # Mark as viewed
        self.mark_as_viewed()

        # Auto-close after 15 seconds
        self.auto_close_timer = self.viewer_window.after(15000, self.close_viewer)

    def setup_viewer_ui(self):
        """Set up the viewer UI."""
        # Progress bar at top
        self.progress_bar = tk.Canvas(self.viewer_window, height=4, bg='black', highlightthickness=0)
        self.progress_bar.pack(fill=tk.X, pady=(10, 0))

        # User info at top
        info_frame = tk.Frame(self.viewer_window, bg='black')
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        user_label = tk.Label(info_frame, text=self.status.get('username', 'Unknown'),
                             bg='black', fg='white', font=("Arial", 14, "bold"))
        user_label.pack(side=tk.LEFT)

        time_ago = self.get_time_ago()
        time_label = tk.Label(info_frame, text=time_ago,
                             bg='black', fg='gray', font=("Arial", 10))
        time_label.pack(side=tk.LEFT, padx=(10, 0))

        # Close button
        close_btn = tk.Button(info_frame, text="×", bg='black', fg='white',
                             bd=0, font=("Arial", 20), command=self.close_viewer)
        close_btn.pack(side=tk.RIGHT)

        # Status content
        content_frame = tk.Frame(self.viewer_window, bg='black')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        if self.status.get('content') and not self.status.get('media_url'):
            # Text status
            self.show_text_status(content_frame)
        elif self.status.get('media_url'):
            # Media status
            self.show_media_status(content_frame)

        # Start progress animation
        self.animate_progress()

    def show_text_status(self, parent):
        """Show text status."""
        bg_color = self.status.get('background_color', '#4ECDC4')

        # Create colored background
        status_frame = tk.Frame(parent, bg=bg_color)
        status_frame.pack(fill=tk.BOTH, expand=True)

        # Status text
        content = self.status.get('content', '')
        text_label = tk.Label(status_frame, text=content,
                             bg=bg_color, fg='white',
                             font=("Arial", 24, "bold"),
                             wraplength=600, justify=tk.CENTER)
        text_label.pack(expand=True)

    def show_media_status(self, parent):
        """Show media status."""
        media_url = self.status.get('media_url')
        media_type = self.status.get('media_type', 'image')

        if media_type == 'image':
            try:
                # Load and display image
                img = Image.open(media_url)

                # Resize to fit screen
                screen_width = self.viewer_window.winfo_screenwidth()
                screen_height = self.viewer_window.winfo_screenheight()
                img.thumbnail((screen_width-100, screen_height-200), Image.Resampling.LANCZOS)

                photo = ImageTk.PhotoImage(img)

                img_label = tk.Label(parent, image=photo, bg='black')
                img_label.image = photo  # Keep reference
                img_label.pack(expand=True)

            except Exception as e:
                # Fallback
                error_label = tk.Label(parent, text="Unable to load image",
                                     bg='black', fg='white', font=("Arial", 16))
                error_label.pack(expand=True)

        # Caption if any
        caption = self.status.get('content', '').strip()
        if caption:
            caption_label = tk.Label(parent, text=caption,
                                   bg='black', fg='white', font=("Arial", 14),
                                   wraplength=600, justify=tk.CENTER)
            caption_label.pack(pady=20)

    def get_time_ago(self) -> str:
        """Get time ago string."""
        created_at = datetime.fromisoformat(self.status['created_at'].replace('Z', '+00:00'))
        now = datetime.now(created_at.tzinfo)
        diff = now - created_at

        if diff.days > 0:
            return f"{diff.days}d ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}h ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}m ago"
        else:
            return "Just now"

    def animate_progress(self):
        """Animate progress bar."""
        if not self.progress_bar:
            return

        # Clear and redraw progress
        self.progress_bar.delete("all")

        width = self.progress_bar.winfo_width()
        if width > 1:
            # Calculate progress (15 seconds total)
            elapsed = 15000 - (self.auto_close_timer and
                              self.viewer_window.tk.call('after', 'info', self.auto_close_timer) or 0)
            progress = min(elapsed / 15000, 1.0)

            # Draw progress bar
            progress_width = int(width * progress)
            self.progress_bar.create_rectangle(0, 0, progress_width, 4, fill='white', outline='')
            self.progress_bar.create_rectangle(progress_width, 0, width, 4, fill='gray', outline='')

        # Schedule next update
        if self.viewer_window:
            self.viewer_window.after(100, self.animate_progress)

    def mark_as_viewed(self):
        """Mark status as viewed."""
        def mark_thread():
            try:
                self.api_client.view_status_update(self.status['id'])
            except Exception as e:
                print(f"Error marking status as viewed: {e}")

        threading.Thread(target=mark_thread, daemon=True).start()

    def close_viewer(self):
        """Close the status viewer."""
        if self.auto_close_timer:
            self.viewer_window.after_cancel(self.auto_close_timer)

        if self.viewer_window:
            self.viewer_window.destroy()
            self.viewer_window = None