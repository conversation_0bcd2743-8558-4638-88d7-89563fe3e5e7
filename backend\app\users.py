from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

# SQLAlchemy User Model
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    phone = Column(String(20), unique=True, nullable=True)
    hashed_password = Column(String(255), nullable=False)

    # Profile information
    full_name = Column(String(100), nullable=True)
    avatar_url = Column(String(255), nullable=True)
    status_message = Column(String(255), nullable=True)  # WhatsApp-like status
    bio = Column(Text, nullable=True)

    # Privacy settings
    show_last_seen = Column(Boolean, default=True)
    show_profile_photo = Column(Boolean, default=True)
    show_status = Column(Boolean, default=True)

    # Status and activity
    is_active = Column(Boolean, default=True)
    is_online = Column(Boolean, default=False)
    last_seen = Column(DateTime(timezone=True), server_default=func.now())

    # Two-factor authentication
    two_factor_enabled = Column(Boolean, default=False)
    two_factor_secret = Column(String(255), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    chat_memberships = relationship("ChatMember", back_populates="user")
    contacts = relationship("Contact", foreign_keys="Contact.user_id", back_populates="user")
    contact_of = relationship("Contact", foreign_keys="Contact.contact_user_id", back_populates="contact_user")
    status_updates = relationship("StatusUpdate", back_populates="user")

# Pydantic Models for API
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str
    phone: Optional[str] = None

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    status_message: Optional[str] = None
    bio: Optional[str] = None
    show_last_seen: Optional[bool] = None
    show_profile_photo: Optional[bool] = None
    show_status: Optional[bool] = None

class UserProfile(BaseModel):
    id: int
    username: str
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    status_message: Optional[str] = None
    bio: Optional[str] = None
    is_online: bool
    last_seen: Optional[datetime] = None
    show_last_seen: bool
    show_profile_photo: bool
    show_status: bool

    class Config:
        from_attributes = True

class UserResponse(UserBase):
    id: int
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    status_message: Optional[str] = None
    bio: Optional[str] = None
    is_active: bool
    is_online: bool
    last_seen: datetime
    created_at: datetime
    show_last_seen: bool
    show_profile_photo: bool
    show_status: bool

    class Config:
        from_attributes = True

class ContactCreate(BaseModel):
    contact_user_id: int
    contact_name: Optional[str] = None

class ContactResponse(BaseModel):
    id: int
    contact_user_id: int
    contact_name: Optional[str] = None
    contact_username: str
    contact_full_name: Optional[str] = None
    contact_avatar_url: Optional[str] = None
    contact_status_message: Optional[str] = None
    is_blocked: bool
    is_favorite: bool
    is_online: bool
    last_seen: Optional[datetime] = None
    added_at: datetime

    class Config:
        from_attributes = True

class StatusUpdateCreate(BaseModel):
    content: Optional[str] = None
    media_url: Optional[str] = None
    media_type: Optional[str] = None
    background_color: Optional[str] = None
    font_style: Optional[str] = None
    privacy_setting: str = "all"

class StatusUpdateResponse(BaseModel):
    id: int
    user_id: int
    username: str
    user_avatar_url: Optional[str] = None
    content: Optional[str] = None
    media_url: Optional[str] = None
    media_type: Optional[str] = None
    background_color: Optional[str] = None
    font_style: Optional[str] = None
    privacy_setting: str
    view_count: int
    expires_at: datetime
    created_at: datetime
    is_viewed: Optional[bool] = False  # Whether current user viewed it

    class Config:
        from_attributes = True

class UserLogin(BaseModel):
    username: str
    password: str

class UserPresence(BaseModel):
    user_id: int
    username: str
    is_online: bool
    last_seen: datetime

# Chat Room Models
class ChatRoom(Base):
    __tablename__ = "chat_rooms"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    is_group = Column(Boolean, default=False)  # False for private chats, True for group chats
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    members = relationship("ChatMember", back_populates="chat_room")

class ChatMember(Base):
    __tablename__ = "chat_members"

    id = Column(Integer, primary_key=True, index=True)
    chat_room_id = Column(Integer, ForeignKey("chat_rooms.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    joined_at = Column(DateTime(timezone=True), server_default=func.now())
    is_admin = Column(Boolean, default=False)
    role = Column(String(20), default="member")

    # Relationships
    user = relationship("User", back_populates="chat_memberships")
    chat_room = relationship("ChatRoom", back_populates="members")

# Pydantic models for Chat Rooms
class ChatRoomBase(BaseModel):
    name: str
    description: Optional[str] = None
    is_group: bool = False

class ChatRoomCreate(ChatRoomBase):
    pass

class ChatRoomResponse(ChatRoomBase):
    id: int
    created_by: int
    created_at: datetime
    member_count: Optional[int] = 0
    
    class Config:
        from_attributes = True

# Contact Management
class Contact(Base):
    __tablename__ = "contacts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    contact_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    contact_name = Column(String(100), nullable=True)  # Custom name for contact
    is_blocked = Column(Boolean, default=False)
    is_favorite = Column(Boolean, default=False)
    added_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="contacts")
    contact_user = relationship("User", foreign_keys=[contact_user_id], back_populates="contact_of")

# Status Updates (WhatsApp-like stories)
class StatusUpdate(Base):
    __tablename__ = "status_updates"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    content = Column(Text, nullable=True)  # Text status
    media_url = Column(String(255), nullable=True)  # Image/video status
    media_type = Column(String(20), nullable=True)  # image, video
    background_color = Column(String(7), nullable=True)  # Hex color for text status
    font_style = Column(String(50), nullable=True)

    # Privacy and expiration
    expires_at = Column(DateTime(timezone=True), nullable=False)  # 24 hours from creation
    privacy_setting = Column(String(20), default="all")  # all, contacts, selected

    # Interaction tracking
    view_count = Column(Integer, default=0)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="status_updates")
    views = relationship("StatusView", back_populates="status")

# Status Views (who viewed the status)
class StatusView(Base):
    __tablename__ = "status_views"

    id = Column(Integer, primary_key=True, index=True)
    status_id = Column(Integer, ForeignKey("status_updates.id"), nullable=False)
    viewer_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    viewed_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    status = relationship("StatusUpdate", back_populates="views")
    viewer = relationship("User")

class ChatMemberResponse(BaseModel):
    id: int
    user_id: int
    username: str
    is_admin: bool
    joined_at: datetime
    
    class Config:
        from_attributes = True
