#!/usr/bin/env python3
"""
Simple test script to validate the chat API endpoints.
Run this after starting the backend server to test basic functionality.
"""

import requests
import json
import time
from typing import Dict, Any, Optional

class ChatAPITester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.access_token: Optional[str] = None
        self.user_id: Optional[int] = None
        self.username: Optional[str] = None
    
    def test_health_check(self) -> bool:
        """Test health check endpoint."""
        print("Testing health check...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ Health check passed")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    def test_user_registration(self, username: str, email: str, password: str) -> bool:
        """Test user registration."""
        print(f"Testing user registration for {username}...")
        try:
            data = {
                "username": username,
                "email": email,
                "password": password,
                "full_name": f"Test User {username}"
            }
            response = self.session.post(f"{self.base_url}/auth/register", json=data)
            
            if response.status_code == 200:
                print("✅ User registration passed")
                return True
            else:
                print(f"❌ User registration failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ User registration error: {e}")
            return False
    
    def test_user_login(self, username: str, password: str) -> bool:
        """Test user login."""
        print(f"Testing user login for {username}...")
        try:
            data = {"username": username, "password": password}
            response = self.session.post(f"{self.base_url}/auth/login", json=data)
            
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data["access_token"]
                self.username = username
                print("✅ User login passed")
                return True
            else:
                print(f"❌ User login failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ User login error: {e}")
            return False
    
    def test_get_current_user(self) -> bool:
        """Test getting current user info."""
        print("Testing get current user...")
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.session.get(f"{self.base_url}/users/me", headers=headers)
            
            if response.status_code == 200:
                user_data = response.json()
                self.user_id = user_data["id"]
                print("✅ Get current user passed")
                return True
            else:
                print(f"❌ Get current user failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Get current user error: {e}")
            return False
    
    def test_create_chat_room(self, room_name: str) -> Optional[int]:
        """Test creating a chat room."""
        print(f"Testing chat room creation: {room_name}...")
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            data = {
                "name": room_name,
                "description": f"Test room {room_name}",
                "is_group": True
            }
            response = self.session.post(f"{self.base_url}/chat-rooms", json=data, headers=headers)
            
            if response.status_code == 200:
                room_data = response.json()
                room_id = room_data["id"]
                print("✅ Chat room creation passed")
                return room_id
            else:
                print(f"❌ Chat room creation failed: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"❌ Chat room creation error: {e}")
            return None
    
    def test_get_chat_rooms(self) -> bool:
        """Test getting chat rooms."""
        print("Testing get chat rooms...")
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.session.get(f"{self.base_url}/chat-rooms", headers=headers)
            
            if response.status_code == 200:
                rooms = response.json()
                print(f"✅ Get chat rooms passed - found {len(rooms)} rooms")
                return True
            else:
                print(f"❌ Get chat rooms failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Get chat rooms error: {e}")
            return False
    
    def test_send_message(self, room_id: int, content: str) -> bool:
        """Test sending a message."""
        print(f"Testing send message to room {room_id}...")
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            data = {
                "chat_room_id": room_id,
                "content": content,
                "message_type": "text"
            }
            response = self.session.post(f"{self.base_url}/chat-rooms/{room_id}/messages", 
                                       json=data, headers=headers)
            
            if response.status_code == 200:
                print("✅ Send message passed")
                return True
            else:
                print(f"❌ Send message failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Send message error: {e}")
            return False
    
    def test_get_messages(self, room_id: int) -> bool:
        """Test getting messages."""
        print(f"Testing get messages from room {room_id}...")
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.session.get(f"{self.base_url}/chat-rooms/{room_id}/messages", 
                                      headers=headers)
            
            if response.status_code == 200:
                messages = response.json()
                print(f"✅ Get messages passed - found {len(messages)} messages")
                return True
            else:
                print(f"❌ Get messages failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Get messages error: {e}")
            return False
    
    def test_logout(self) -> bool:
        """Test user logout."""
        print("Testing user logout...")
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = self.session.post(f"{self.base_url}/auth/logout", headers=headers)
            
            if response.status_code == 200:
                print("✅ User logout passed")
                return True
            else:
                print(f"❌ User logout failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ User logout error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests."""
        print("🚀 Starting Chat API Tests\n")
        
        # Generate unique test data
        timestamp = int(time.time())
        test_username = f"testuser_{timestamp}"
        test_email = f"test_{timestamp}@example.com"
        test_password = "testpassword123"
        test_room_name = f"Test Room {timestamp}"
        
        tests_passed = 0
        total_tests = 0
        
        # Test health check
        total_tests += 1
        if self.test_health_check():
            tests_passed += 1
        
        # Test user registration
        total_tests += 1
        if self.test_user_registration(test_username, test_email, test_password):
            tests_passed += 1
        
        # Test user login
        total_tests += 1
        if self.test_user_login(test_username, test_password):
            tests_passed += 1
        
        # Test get current user
        total_tests += 1
        if self.test_get_current_user():
            tests_passed += 1
        
        # Test create chat room
        total_tests += 1
        room_id = self.test_create_chat_room(test_room_name)
        if room_id:
            tests_passed += 1
        
        # Test get chat rooms
        total_tests += 1
        if self.test_get_chat_rooms():
            tests_passed += 1
        
        # Test send message (only if room was created)
        if room_id:
            total_tests += 1
            if self.test_send_message(room_id, "Hello, this is a test message!"):
                tests_passed += 1
            
            # Test get messages
            total_tests += 1
            if self.test_get_messages(room_id):
                tests_passed += 1
        
        # Test logout
        total_tests += 1
        if self.test_logout():
            tests_passed += 1
        
        # Print results
        print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")
        
        if tests_passed == total_tests:
            print("🎉 All tests passed! The API is working correctly.")
        else:
            print("⚠️  Some tests failed. Please check the backend server and try again.")
        
        return tests_passed == total_tests

def main():
    """Main function to run tests."""
    print("Chat API Test Suite")
    print("===================")
    print("Make sure the backend server is running on http://localhost:8000")
    print()
    
    tester = ChatAPITester()
    success = tester.run_all_tests()
    
    if success:
        exit(0)
    else:
        exit(1)

if __name__ == "__main__":
    main()
