#!/usr/bin/env python3
"""
Test script to verify the modern chat interface works correctly.
This will help identify and fix any issues with the WhatsApp-like features.
"""

import os
import sys
import subprocess
import time
import requests
import threading

def check_backend():
    """Check if backend is running and healthy."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def create_test_users():
    """Create test users for testing."""
    print("👥 Creating test users...")
    
    test_users = [
        {
            "username": "alice",
            "email": "<EMAIL>",
            "password": "testpass123",
            "full_name": "<PERSON>"
        },
        {
            "username": "bob", 
            "email": "<EMAIL>",
            "password": "testpass123",
            "full_name": "<PERSON>"
        }
    ]
    
    created_users = []
    
    for user_data in test_users:
        try:
            response = requests.post("http://localhost:8001/auth/register", json=user_data)
            if response.status_code == 200:
                print(f"✅ Created user: {user_data['username']}")
                created_users.append(user_data)
            else:
                print(f"⚠️ User {user_data['username']} might already exist")
                created_users.append(user_data)  # Assume it exists
        except Exception as e:
            print(f"❌ Error creating user {user_data['username']}: {e}")
    
    return created_users

def create_test_room():
    """Create a test chat room."""
    print("🏠 Creating test chat room...")
    
    # Login as first user to create room
    try:
        login_response = requests.post("http://localhost:8001/auth/login", json={
            "username": "alice",
            "password": "testpass123"
        })
        
        if login_response.status_code == 200:
            token = login_response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            
            # Create room
            room_response = requests.post("http://localhost:8001/chat-rooms", 
                                        json={
                                            "name": "Test Chat Room",
                                            "description": "A test room for modern chat features",
                                            "is_group": True
                                        },
                                        headers=headers)
            
            if room_response.status_code == 200:
                room = room_response.json()
                print(f"✅ Created test room: {room['name']} (ID: {room['id']})")
                return room
            else:
                print(f"❌ Failed to create room: {room_response.text}")
        
    except Exception as e:
        print(f"❌ Error creating test room: {e}")
    
    return None

def setup_general_chat():
    """Set up general chat room."""
    print("🏠 Setting up General Chat...")
    
    try:
        # Run the setup script
        result = subprocess.run(
            ["python", "setup_general_chat.py"],
            cwd="backend",
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ General Chat setup completed")
            return True
        else:
            print(f"❌ General Chat setup failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up General Chat: {e}")
        return False

def test_api_endpoints():
    """Test key API endpoints."""
    print("🧪 Testing API endpoints...")
    
    # Test login
    try:
        response = requests.post("http://localhost:8001/auth/login", json={
            "username": "alice",
            "password": "testpass123"
        })
        
        if response.status_code == 200:
            token = response.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ Login working")
            
            # Test get chat rooms
            rooms_response = requests.get("http://localhost:8001/chat-rooms", headers=headers)
            if rooms_response.status_code == 200:
                rooms = rooms_response.json()
                print(f"✅ Chat rooms API working ({len(rooms)} rooms found)")
            else:
                print(f"❌ Chat rooms API failed: {rooms_response.status_code}")
            
            # Test get users
            users_response = requests.get("http://localhost:8001/users", headers=headers)
            if users_response.status_code == 200:
                users = users_response.json()
                print(f"✅ Users API working ({len(users)} users found)")
            else:
                print(f"❌ Users API failed: {users_response.status_code}")
                
        else:
            print(f"❌ Login failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API test error: {e}")

def start_client_test():
    """Start the client for testing."""
    print("🖥️ Starting client application...")
    print("=" * 50)
    print("🎯 TESTING INSTRUCTIONS:")
    print("1. Login with username: 'alice' password: 'testpass123'")
    print("2. Look for the modern WhatsApp-like interface")
    print("3. Check if you can see:")
    print("   - Modern green color scheme")
    print("   - Chat list on the left")
    print("   - General Chat room")
    print("   - Other users in the list")
    print("4. Try clicking on 'General Chat' to join")
    print("5. Try sending a message")
    print("=" * 50)
    
    try:
        subprocess.run(["python", "main.py"], cwd="client")
    except KeyboardInterrupt:
        print("\n👋 Client testing stopped")
    except Exception as e:
        print(f"❌ Client error: {e}")

def main():
    """Main test function."""
    print("🧪 Modern Chat Interface Test")
    print("=" * 50)
    
    # Check backend
    if not check_backend():
        print("❌ Backend is not running!")
        print("Please start the backend first:")
        print("cd backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8001")
        return
    
    print("✅ Backend is running")
    
    # Create test users
    create_test_users()
    
    # Setup general chat
    setup_general_chat()
    
    # Create test room
    create_test_room()
    
    # Test API endpoints
    test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("🎉 Setup complete! Ready to test modern chat interface")
    
    # Ask user if they want to start the client
    choice = input("\nStart the client application for testing? (y/n): ").strip().lower()
    
    if choice == 'y':
        start_client_test()
    else:
        print("👋 Test setup complete. You can manually start the client:")
        print("cd client && python main.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Test interrupted")
    except Exception as e:
        print(f"❌ Test error: {e}")
