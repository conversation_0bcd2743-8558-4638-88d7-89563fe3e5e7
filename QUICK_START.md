# Quick Start Guide 🚀

Get your real-time chat application running in under 5 minutes!

## 📋 Prerequisites

- **Python 3.8+** installed on your system
- **Internet connection** for downloading dependencies
- **Windows, macOS, or Linux** (cross-platform compatible)

## ⚡ Option 1: Automatic Setup (Recommended)

### Step 1: Download and Setup
```bash
# Navigate to the project directory
cd "Chat Application"

# Run the automatic installer
python install.py
```

### Step 2: Start the Application
```bash
# Start both backend and client automatically
python start_dev.py
```

**That's it!** The installer will:
- ✅ Check your Python version
- ✅ Install all dependencies
- ✅ Create configuration files
- ✅ Start the backend server
- ✅ Launch the client application

## 🔧 Option 2: Manual Setup

### Step 1: Install Backend Dependencies
```bash
cd backend
pip install -r requirements.txt
```

### Step 2: Install Client Dependencies
```bash
cd ../client
pip install -r requirements.txt
```

### Step 3: Start Backend Server
```bash
cd ../backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8001
```

### Step 4: Start Client (New Terminal)
```bash
cd client
python main.py
```

## 🎯 Using the Application

### First Time Setup

1. **Launch the Client**: A GUI window will appear
2. **Register Account**: Click "Register" tab and create a new account
3. **Login**: Use your credentials to log in
4. **Create Room**: Click "+ New Room" to create a chat room
5. **Start Chatting**: Send messages in real-time!

### Key Features to Try

- **Real-time Messaging**: Type and send messages instantly
- **Typing Indicators**: See when others are typing
- **File Upload**: Click the 📎 button to share files
- **Multiple Rooms**: Create and join different chat rooms
- **Online Status**: See who's currently online

## 🌐 Accessing the API

While the client is running, you can also access:

- **API Documentation**: http://localhost:8001/docs
- **Health Check**: http://localhost:8001/health
- **API Base**: http://localhost:8001

## 🧪 Testing the Installation

Run the automated test suite:
```bash
cd backend
python test_api.py
```

This will verify that all API endpoints are working correctly.

## 🔧 Configuration

### Backend Configuration
Edit `backend/.env`:
```env
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite+aiosqlite:///./chat_app.db
DEBUG=True
```

### Client Configuration
Edit `client/.env`:
```env
API_BASE_URL=http://localhost:8001
```

## 🐛 Troubleshooting

### Common Issues

**1. Port 8001 already in use**
```bash
# Change port in backend startup command
python -m uvicorn app.main:app --host 0.0.0.0 --port 8002

# Update client/.env
API_BASE_URL=http://localhost:8002
```

**2. Import errors**
```bash
# Reinstall dependencies
pip install -r requirements.txt
```

**3. Database errors**
```bash
# Reset database
rm backend/chat_app.db
# Restart backend server
```

**4. WebSocket connection failed**
- Ensure backend is running
- Check firewall settings
- Verify API_BASE_URL in client/.env

### Getting Help

1. **Check logs** in the terminal for error messages
2. **Run tests** with `python backend/test_api.py`
3. **Verify installation** with `python install.py`
4. **Check documentation** in README.md

## 🚀 Next Steps

### For Development
- Explore the code in `backend/app/` and `client/`
- Modify the UI in `client/chat_window.py`
- Add new API endpoints in `backend/app/main.py`
- Customize the database models in `backend/app/users.py`

### For Deployment
- See [DEPLOYMENT.md](DEPLOYMENT.md) for cloud deployment
- Use Docker with `docker-compose up`
- Deploy to Render, Railway, or Heroku

### For Production
- Change `SECRET_KEY` in backend/.env
- Use PostgreSQL instead of SQLite
- Enable HTTPS/WSS
- Set up monitoring and backups

## 📞 Support

If you encounter any issues:

1. **Check the logs** for error messages
2. **Run the test suite** to verify functionality
3. **Review the documentation** for detailed guides
4. **Check the troubleshooting section** above

## 🎉 Success!

If everything is working correctly, you should see:

- ✅ Backend server running on http://localhost:8001
- ✅ Client GUI application window open
- ✅ Ability to register, login, and send messages
- ✅ Real-time message delivery
- ✅ WebSocket connection status showing "Connected"

**Congratulations! Your real-time chat application is now running!** 🎊

---

**Enjoy chatting in real-time!** 💬✨
