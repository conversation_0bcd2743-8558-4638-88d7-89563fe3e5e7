import tkinter as tk
from tkinter import ttk
import threading
import time
import os
import json
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timedelta
from pathlib import Path
import pygame
from plyer import notification

class NotificationManager:
    """Advanced notification system for the chat application."""
    
    def __init__(self, parent_window: tk.Tk):
        self.parent_window = parent_window
        self.settings_file = Path.home() / ".chat_app_notifications.json"
        
        # Default settings
        self.settings = {
            'desktop_notifications': True,
            'sound_notifications': True,
            'popup_notifications': True,
            'notification_sound': 'default',
            'quiet_hours_enabled': False,
            'quiet_hours_start': '22:00',
            'quiet_hours_end': '08:00',
            'show_message_preview': True,
            'notification_position': 'top_right',
            'auto_dismiss_time': 5000,  # milliseconds
            'vibration_enabled': False,
            'group_notifications': True,
            'contact_notifications': True
        }
        
        # Load saved settings
        self.load_settings()
        
        # Initialize sound system
        self.init_sound_system()
        
        # Active notifications
        self.active_notifications: List[NotificationPopup] = []
        
        # Notification queue
        self.notification_queue: List[Dict[str, Any]] = []
        self.processing_queue = False
    
    def init_sound_system(self):
        """Initialize pygame for sound notifications."""
        try:
            pygame.mixer.init()
            self.sound_initialized = True
            
            # Load notification sounds
            self.sounds = {
                'default': self.load_sound('notification.wav'),
                'message': self.load_sound('message.wav'),
                'mention': self.load_sound('mention.wav'),
                'call': self.load_sound('call.wav')
            }
        except Exception as e:
            print(f"Sound system initialization failed: {e}")
            self.sound_initialized = False
            self.sounds = {}
    
    def load_sound(self, filename: str) -> Optional[pygame.mixer.Sound]:
        """Load a sound file."""
        try:
            sound_path = Path(__file__).parent / "assets" / "sounds" / filename
            if sound_path.exists():
                return pygame.mixer.Sound(str(sound_path))
        except Exception as e:
            print(f"Failed to load sound {filename}: {e}")
        return None
    
    def load_settings(self):
        """Load notification settings from file."""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r') as f:
                    saved_settings = json.load(f)
                    self.settings.update(saved_settings)
        except Exception as e:
            print(f"Failed to load notification settings: {e}")
    
    def save_settings(self):
        """Save notification settings to file."""
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(self.settings, f, indent=2)
        except Exception as e:
            print(f"Failed to save notification settings: {e}")
    
    def is_quiet_hours(self) -> bool:
        """Check if current time is within quiet hours."""
        if not self.settings['quiet_hours_enabled']:
            return False
        
        now = datetime.now().time()
        start_time = datetime.strptime(self.settings['quiet_hours_start'], '%H:%M').time()
        end_time = datetime.strptime(self.settings['quiet_hours_end'], '%H:%M').time()
        
        if start_time <= end_time:
            return start_time <= now <= end_time
        else:  # Quiet hours span midnight
            return now >= start_time or now <= end_time
    
    def should_show_notification(self, notification_type: str) -> bool:
        """Determine if notification should be shown based on settings."""
        if self.is_quiet_hours():
            return False
        
        if notification_type == 'message' and not self.settings['contact_notifications']:
            return False
        
        if notification_type == 'group_message' and not self.settings['group_notifications']:
            return False
        
        return True
    
    def show_notification(self, title: str, message: str, notification_type: str = 'message',
                         sender_info: Optional[Dict[str, Any]] = None,
                         chat_room_info: Optional[Dict[str, Any]] = None,
                         on_click: Optional[Callable] = None):
        """Show a notification with multiple methods."""
        
        if not self.should_show_notification(notification_type):
            return
        
        notification_data = {
            'title': title,
            'message': message,
            'type': notification_type,
            'sender_info': sender_info,
            'chat_room_info': chat_room_info,
            'on_click': on_click,
            'timestamp': datetime.now()
        }
        
        # Add to queue
        self.notification_queue.append(notification_data)
        
        # Process queue
        if not self.processing_queue:
            threading.Thread(target=self._process_notification_queue, daemon=True).start()
    
    def _process_notification_queue(self):
        """Process notification queue in background."""
        self.processing_queue = True
        
        while self.notification_queue:
            notification_data = self.notification_queue.pop(0)
            
            # Show desktop notification
            if self.settings['desktop_notifications']:
                self._show_desktop_notification(notification_data)
            
            # Show popup notification
            if self.settings['popup_notifications']:
                self._show_popup_notification(notification_data)
            
            # Play sound
            if self.settings['sound_notifications']:
                self._play_notification_sound(notification_data['type'])
            
            # Flash taskbar (Windows)
            self._flash_taskbar()
            
            # Small delay between notifications
            time.sleep(0.5)
        
        self.processing_queue = False
    
    def _show_desktop_notification(self, notification_data: Dict[str, Any]):
        """Show system desktop notification."""
        try:
            title = notification_data['title']
            message = notification_data['message']
            
            if not self.settings['show_message_preview']:
                message = "New message"
            
            notification.notify(
                title=title,
                message=message,
                app_name="Chat Application",
                timeout=self.settings['auto_dismiss_time'] // 1000
            )
        except Exception as e:
            print(f"Desktop notification failed: {e}")
    
    def _show_popup_notification(self, notification_data: Dict[str, Any]):
        """Show custom popup notification."""
        def show_popup():
            popup = NotificationPopup(
                self.parent_window,
                notification_data,
                self.settings,
                on_dismiss=lambda p: self._dismiss_popup(p)
            )
            self.active_notifications.append(popup)
            popup.show()
        
        # Schedule on main thread
        self.parent_window.after(0, show_popup)
    
    def _play_notification_sound(self, notification_type: str):
        """Play notification sound."""
        if not self.sound_initialized:
            return
        
        sound_name = 'default'
        if notification_type in ['message', 'group_message']:
            sound_name = 'message'
        elif notification_type == 'mention':
            sound_name = 'mention'
        elif notification_type in ['call', 'video_call']:
            sound_name = 'call'
        
        sound = self.sounds.get(sound_name)
        if sound:
            try:
                sound.play()
            except Exception as e:
                print(f"Failed to play sound: {e}")
    
    def _flash_taskbar(self):
        """Flash the taskbar to get user attention."""
        try:
            # Windows-specific taskbar flashing
            import ctypes
            from ctypes import wintypes
            
            # Get window handle
            hwnd = self.parent_window.winfo_id()
            
            # Flash window
            ctypes.windll.user32.FlashWindow(hwnd, True)
        except Exception:
            pass  # Ignore on non-Windows systems
    
    def _dismiss_popup(self, popup):
        """Dismiss a popup notification."""
        if popup in self.active_notifications:
            self.active_notifications.remove(popup)
    
    def dismiss_all_notifications(self):
        """Dismiss all active popup notifications."""
        for popup in self.active_notifications[:]:
            popup.dismiss()
        self.active_notifications.clear()
    
    def show_settings_dialog(self):
        """Show notification settings dialog."""
        settings_dialog = NotificationSettingsDialog(self.parent_window, self)
        settings_dialog.show()

class NotificationPopup:
    """Custom popup notification window."""
    
    def __init__(self, parent: tk.Tk, notification_data: Dict[str, Any], 
                 settings: Dict[str, Any], on_dismiss: Callable):
        self.parent = parent
        self.notification_data = notification_data
        self.settings = settings
        self.on_dismiss = on_dismiss
        
        self.popup_window = None
        self.auto_dismiss_timer = None
    
    def show(self):
        """Show the popup notification."""
        # Create popup window
        self.popup_window = tk.Toplevel(self.parent)
        self.popup_window.title("")
        self.popup_window.geometry("350x100")
        self.popup_window.resizable(False, False)
        self.popup_window.attributes('-topmost', True)
        self.popup_window.overrideredirect(True)  # Remove window decorations
        
        # Position popup
        self._position_popup()
        
        # Style popup
        self.popup_window.configure(bg='#2C3E50')
        
        # Create content
        self._create_popup_content()
        
        # Bind events
        self.popup_window.bind('<Button-1>', self._on_click)
        self.popup_window.bind('<Enter>', self._on_hover)
        self.popup_window.bind('<Leave>', self._on_leave)
        
        # Auto-dismiss timer
        if self.settings['auto_dismiss_time'] > 0:
            self.auto_dismiss_timer = self.popup_window.after(
                self.settings['auto_dismiss_time'], 
                self.dismiss
            )
    
    def _position_popup(self):
        """Position the popup based on settings."""
        screen_width = self.popup_window.winfo_screenwidth()
        screen_height = self.popup_window.winfo_screenheight()
        
        popup_width = 350
        popup_height = 100
        
        position = self.settings['notification_position']
        
        if position == 'top_right':
            x = screen_width - popup_width - 20
            y = 20
        elif position == 'top_left':
            x = 20
            y = 20
        elif position == 'bottom_right':
            x = screen_width - popup_width - 20
            y = screen_height - popup_height - 60
        elif position == 'bottom_left':
            x = 20
            y = screen_height - popup_height - 60
        else:  # center
            x = (screen_width - popup_width) // 2
            y = (screen_height - popup_height) // 2
        
        # Adjust for multiple notifications
        offset = len([p for p in self.on_dismiss.__self__.active_notifications if p != self]) * 110
        if 'top' in position:
            y += offset
        else:
            y -= offset
        
        self.popup_window.geometry(f"{popup_width}x{popup_height}+{x}+{y}")
    
    def _create_popup_content(self):
        """Create the popup content."""
        main_frame = tk.Frame(self.popup_window, bg='#34495E', relief='solid', bd=1)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Header with title and close button
        header_frame = tk.Frame(main_frame, bg='#34495E', height=30)
        header_frame.pack(fill=tk.X, padx=10, pady=(5, 0))
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame,
                              text=self.notification_data['title'],
                              bg='#34495E',
                              fg='white',
                              font=('Arial', 10, 'bold'),
                              anchor=tk.W)
        title_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        close_button = tk.Button(header_frame,
                                text="×",
                                bg='#34495E',
                                fg='white',
                                bd=0,
                                font=('Arial', 12, 'bold'),
                                command=self.dismiss)
        close_button.pack(side=tk.RIGHT)
        
        # Message content
        message_frame = tk.Frame(main_frame, bg='#34495E')
        message_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 5))
        
        message_text = self.notification_data['message']
        if not self.settings['show_message_preview']:
            message_text = "New message received"
        
        message_label = tk.Label(message_frame,
                                text=message_text,
                                bg='#34495E',
                                fg='#BDC3C7',
                                font=('Arial', 9),
                                wraplength=300,
                                justify=tk.LEFT,
                                anchor=tk.W)
        message_label.pack(fill=tk.BOTH, expand=True)
        
        # Timestamp
        timestamp = self.notification_data['timestamp'].strftime("%H:%M")
        time_label = tk.Label(message_frame,
                             text=timestamp,
                             bg='#34495E',
                             fg='#7F8C8D',
                             font=('Arial', 8),
                             anchor=tk.E)
        time_label.pack(side=tk.RIGHT)
    
    def _on_click(self, event=None):
        """Handle popup click."""
        if self.notification_data.get('on_click'):
            self.notification_data['on_click']()
        self.dismiss()
    
    def _on_hover(self, event=None):
        """Handle mouse hover."""
        if self.auto_dismiss_timer:
            self.popup_window.after_cancel(self.auto_dismiss_timer)
            self.auto_dismiss_timer = None
    
    def _on_leave(self, event=None):
        """Handle mouse leave."""
        if not self.auto_dismiss_timer and self.settings['auto_dismiss_time'] > 0:
            self.auto_dismiss_timer = self.popup_window.after(
                2000,  # Shorter time after hover
                self.dismiss
            )
    
    def dismiss(self):
        """Dismiss the popup."""
        if self.auto_dismiss_timer:
            self.popup_window.after_cancel(self.auto_dismiss_timer)
        
        if self.popup_window:
            self.popup_window.destroy()
            self.popup_window = None
        
        self.on_dismiss(self)

class NotificationSettingsDialog:
    """Dialog for configuring notification settings."""
    
    def __init__(self, parent: tk.Tk, notification_manager: NotificationManager):
        self.parent = parent
        self.notification_manager = notification_manager
        self.dialog = None
    
    def show(self):
        """Show the settings dialog."""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Notification Settings")
        self.dialog.geometry("400x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"400x500+{x}+{y}")
        
        self._create_settings_ui()
    
    def _create_settings_ui(self):
        """Create the settings UI."""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Notification Settings", 
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        row = 1
        
        # Desktop notifications
        self.desktop_var = tk.BooleanVar(value=self.notification_manager.settings['desktop_notifications'])
        desktop_check = ttk.Checkbutton(main_frame, text="Desktop notifications", 
                                       variable=self.desktop_var)
        desktop_check.grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        row += 1
        
        # Sound notifications
        self.sound_var = tk.BooleanVar(value=self.notification_manager.settings['sound_notifications'])
        sound_check = ttk.Checkbutton(main_frame, text="Sound notifications", 
                                     variable=self.sound_var)
        sound_check.grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        row += 1
        
        # Popup notifications
        self.popup_var = tk.BooleanVar(value=self.notification_manager.settings['popup_notifications'])
        popup_check = ttk.Checkbutton(main_frame, text="Popup notifications", 
                                     variable=self.popup_var)
        popup_check.grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        row += 1
        
        # Message preview
        self.preview_var = tk.BooleanVar(value=self.notification_manager.settings['show_message_preview'])
        preview_check = ttk.Checkbutton(main_frame, text="Show message preview", 
                                       variable=self.preview_var)
        preview_check.grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        row += 1
        
        # Quiet hours
        ttk.Separator(main_frame, orient='horizontal').grid(row=row, column=0, columnspan=2, 
                                                           sticky=(tk.W, tk.E), pady=10)
        row += 1
        
        ttk.Label(main_frame, text="Quiet Hours", font=("Arial", 12, "bold")).grid(
            row=row, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        row += 1
        
        self.quiet_hours_var = tk.BooleanVar(value=self.notification_manager.settings['quiet_hours_enabled'])
        quiet_check = ttk.Checkbutton(main_frame, text="Enable quiet hours", 
                                     variable=self.quiet_hours_var)
        quiet_check.grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=5)
        row += 1
        
        # Quiet hours time range
        ttk.Label(main_frame, text="From:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.start_time_var = tk.StringVar(value=self.notification_manager.settings['quiet_hours_start'])
        start_time_entry = ttk.Entry(main_frame, textvariable=self.start_time_var, width=10)
        start_time_entry.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        ttk.Label(main_frame, text="To:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.end_time_var = tk.StringVar(value=self.notification_manager.settings['quiet_hours_end'])
        end_time_entry = ttk.Entry(main_frame, textvariable=self.end_time_var, width=10)
        end_time_entry.grid(row=row, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        row += 1
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="Save", command=self._save_settings).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="Test", command=self._test_notification).grid(row=0, column=2, padx=5)
    
    def _save_settings(self):
        """Save the settings."""
        self.notification_manager.settings.update({
            'desktop_notifications': self.desktop_var.get(),
            'sound_notifications': self.sound_var.get(),
            'popup_notifications': self.popup_var.get(),
            'show_message_preview': self.preview_var.get(),
            'quiet_hours_enabled': self.quiet_hours_var.get(),
            'quiet_hours_start': self.start_time_var.get(),
            'quiet_hours_end': self.end_time_var.get()
        })
        
        self.notification_manager.save_settings()
        self.dialog.destroy()
    
    def _test_notification(self):
        """Test notification with current settings."""
        self.notification_manager.show_notification(
            "Test Notification",
            "This is a test notification to preview your settings.",
            "message"
        )
