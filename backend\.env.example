# Database Configuration
DATABASE_URL=sqlite+aiosqlite:///./chat_app.db
# For PostgreSQL: DATABASE_URL=postgresql+asyncpg://user:password@localhost/chatdb

# JWT Secret Key (CHAN<PERSON> THIS IN PRODUCTION!)
SECRET_KEY=your-secret-key-change-this-in-production

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=uploads

# Server Settings
HOST=0.0.0.0
PORT=8000
DEBUG=False

# Production Settings
ENVIRONMENT=development
