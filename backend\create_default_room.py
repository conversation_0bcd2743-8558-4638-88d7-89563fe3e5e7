#!/usr/bin/env python3
"""
Create a default chat room for users to connect.
This helps solve the issue where users can't find each other.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.ext.asyncio import AsyncSession
from app.database import get_db, init_db
from app.users import Chat<PERSON><PERSON>, ChatMember, User
from sqlalchemy import select

async def create_default_rooms():
    """Create default chat rooms for users to join."""
    print("🏠 Creating default chat rooms...")
    
    # Initialize database
    await init_db()
    
    # Get database session
    async for db in get_db():
        try:
            # Check if default rooms already exist
            result = await db.execute(select(ChatRoom).where(ChatRoom.name == "General Chat"))
            existing_room = result.scalar_one_or_none()
            
            if existing_room:
                print("✅ Default rooms already exist")
                return
            
            # Create default rooms
            default_rooms = [
                {
                    "name": "General Chat",
                    "description": "Welcome! This is the main chat room where everyone can talk.",
                    "is_group": True
                },
                {
                    "name": "Random",
                    "description": "Random discussions and fun conversations.",
                    "is_group": True
                },
                {
                    "name": "Help & Support",
                    "description": "Get help with using the chat application.",
                    "is_group": True
                }
            ]
            
            created_rooms = []
            
            for room_data in default_rooms:
                # Create room
                room = ChatRoom(
                    name=room_data["name"],
                    description=room_data["description"],
                    is_group=room_data["is_group"],
                    created_by=1  # Assume admin user ID is 1
                )
                
                db.add(room)
                await db.flush()  # Get the room ID
                
                created_rooms.append(room)
                print(f"✅ Created room: {room.name}")
            
            # Get all users
            result = await db.execute(select(User))
            users = result.scalars().all()
            
            # Add all users to the General Chat room
            general_room = created_rooms[0]
            for user in users:
                member = ChatMember(
                    chat_room_id=general_room.id,
                    user_id=user.id,
                    role="member"
                )
                db.add(member)
                print(f"✅ Added {user.username} to General Chat")
            
            await db.commit()
            print(f"\n🎉 Successfully created {len(created_rooms)} default rooms!")
            print(f"👥 Added {len(users)} users to General Chat")
            
        except Exception as e:
            print(f"❌ Error creating default rooms: {e}")
            await db.rollback()
        finally:
            await db.close()
            break

async def list_rooms_and_users():
    """List all rooms and users for debugging."""
    print("\n📋 Current Rooms and Users:")
    print("=" * 50)
    
    async for db in get_db():
        try:
            # List rooms
            result = await db.execute(select(ChatRoom))
            rooms = result.scalars().all()
            
            print(f"\n🏠 Chat Rooms ({len(rooms)}):")
            for room in rooms:
                print(f"  • {room.name} (ID: {room.id}) - {room.description}")
                
                # Get members
                result = await db.execute(
                    select(ChatMember, User)
                    .join(User, ChatMember.user_id == User.id)
                    .where(ChatMember.chat_room_id == room.id)
                )
                members = result.all()
                
                if members:
                    member_names = [member[1].username for member in members]
                    print(f"    Members: {', '.join(member_names)}")
                else:
                    print(f"    Members: None")
            
            # List users
            result = await db.execute(select(User))
            users = result.scalars().all()
            
            print(f"\n👥 Users ({len(users)}):")
            for user in users:
                status = "Online" if user.is_online else "Offline"
                print(f"  • {user.username} (ID: {user.id}) - {status}")
            
        except Exception as e:
            print(f"❌ Error listing data: {e}")
        finally:
            await db.close()
            break

def main():
    """Main function."""
    print("🚀 Chat Application - Default Rooms Setup")
    print("=" * 50)
    
    try:
        # Create default rooms
        asyncio.run(create_default_rooms())
        
        # List current state
        asyncio.run(list_rooms_and_users())
        
        print("\n" + "=" * 50)
        print("🎉 Setup complete!")
        print("\nUsers can now:")
        print("1. Join the 'General Chat' room to talk with everyone")
        print("2. Create private chats with other users")
        print("3. Create their own group chats")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")

if __name__ == "__main__":
    main()
