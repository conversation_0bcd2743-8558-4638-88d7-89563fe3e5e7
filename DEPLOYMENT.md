# Deployment Guide

This guide covers deploying the Real-Time Chat Application to various cloud platforms.

## 🚀 Quick Start

### Local Development

1. **Backend Setup**:
   ```bash
   cd backend
   pip install -r requirements.txt
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Client Setup**:
   ```bash
   cd client
   pip install -r requirements.txt
   python main.py
   ```

### Docker Development

```bash
docker-compose up --build
```

## ☁️ Cloud Deployment Options

### 1. Render (Recommended)

Render provides free hosting with automatic deployments from Git.

**Steps:**
1. Fork this repository to your GitHub account
2. Sign up at [render.com](https://render.com)
3. Connect your GitHub account
4. Create a new Web Service from your forked repository
5. Render will automatically detect the `render.yaml` configuration

**Configuration:**
- Build Command: `cd backend && pip install -r requirements.txt`
- Start Command: `cd backend && uvicorn app.main:app --host 0.0.0.0 --port $PORT`
- Environment Variables:
  - `SECRET_KEY`: Generate a secure random key
  - `DATABASE_URL`: Use Render's PostgreSQL add-on

**Database Setup:**
1. Create a PostgreSQL database in Render
2. Copy the connection string to `DATABASE_URL` environment variable

### 2. Railway

Railway offers simple deployment with automatic scaling.

**Steps:**
1. Install Railway CLI: `npm install -g @railway/cli`
2. Login: `railway login`
3. Deploy: `railway up`

**Configuration:**
Railway will use the `railway.json` configuration file automatically.

### 3. Heroku

**Steps:**
1. Install Heroku CLI
2. Create a new Heroku app:
   ```bash
   heroku create your-chat-app-name
   ```
3. Add PostgreSQL addon:
   ```bash
   heroku addons:create heroku-postgresql:hobby-dev
   ```
4. Set environment variables:
   ```bash
   heroku config:set SECRET_KEY=your-secret-key
   ```
5. Deploy:
   ```bash
   git subtree push --prefix backend heroku main
   ```

### 4. DigitalOcean App Platform

**Steps:**
1. Create a new app in DigitalOcean App Platform
2. Connect your GitHub repository
3. Configure the app:
   - Source Directory: `backend`
   - Build Command: `pip install -r requirements.txt`
   - Run Command: `uvicorn app.main:app --host 0.0.0.0 --port $PORT`

### 5. AWS (Advanced)

For production deployments with high availability:

**Using AWS Elastic Beanstalk:**
1. Install EB CLI: `pip install awsebcli`
2. Initialize: `eb init`
3. Create environment: `eb create production`
4. Deploy: `eb deploy`

**Using AWS ECS with Docker:**
1. Build and push Docker image to ECR
2. Create ECS cluster and service
3. Configure load balancer and auto-scaling

## 🔧 Environment Variables

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `SECRET_KEY` | JWT signing key | `your-secret-key-here` |
| `DATABASE_URL` | Database connection string | `******************************` |

### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `CORS_ORIGINS` | Allowed CORS origins | `*` |
| `MAX_FILE_SIZE` | Max upload file size | `10485760` (10MB) |
| `DEBUG` | Debug mode | `False` |

## 🗄️ Database Setup

### SQLite (Development)
No setup required - database file is created automatically.

### PostgreSQL (Production)

1. **Create Database:**
   ```sql
   CREATE DATABASE chatapp;
   CREATE USER chatuser WITH PASSWORD 'your-password';
   GRANT ALL PRIVILEGES ON DATABASE chatapp TO chatuser;
   ```

2. **Set DATABASE_URL:**
   ```
   DATABASE_URL=postgresql+asyncpg://chatuser:password@host:5432/chatapp
   ```

## 🔒 Security Considerations

### Production Checklist

- [ ] Change default `SECRET_KEY`
- [ ] Use PostgreSQL instead of SQLite
- [ ] Enable HTTPS/WSS
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Enable database backups
- [ ] Monitor application logs
- [ ] Set up health checks

### HTTPS/WSS Setup

For WebSocket connections to work properly in production:

1. **Enable HTTPS** on your domain
2. **Update client configuration** to use `wss://` instead of `ws://`
3. **Configure CORS** to allow your frontend domain

## 📱 Client Configuration

Update the client's `.env` file with your deployed backend URL:

```env
API_BASE_URL=https://your-app.onrender.com
```

## 🔍 Monitoring and Logs

### Health Check Endpoint
The API provides a health check at `/health` for monitoring services.

### Logging
Application logs are available through your cloud platform's logging service:
- **Render**: View logs in the dashboard
- **Railway**: Use `railway logs`
- **Heroku**: Use `heroku logs --tail`

## 🚨 Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Ensure HTTPS is enabled
   - Check CORS configuration
   - Verify WebSocket URL uses `wss://`

2. **Database Connection Error**
   - Verify DATABASE_URL format
   - Check database credentials
   - Ensure database server is running

3. **File Upload Issues**
   - Check file size limits
   - Verify upload directory permissions
   - Ensure sufficient disk space

### Debug Mode

Enable debug mode for development:
```env
DEBUG=True
```

**⚠️ Never enable debug mode in production!**

## 📞 Support

If you encounter issues:
1. Check the application logs
2. Verify environment variables
3. Test API endpoints with the test script: `python backend/test_api.py`
4. Review this deployment guide

## 🎯 Performance Optimization

### For High Traffic

1. **Use a CDN** for file uploads
2. **Enable Redis caching** for sessions
3. **Set up load balancing** with multiple instances
4. **Use a dedicated database server**
5. **Implement rate limiting**

### Scaling WebSocket Connections

For applications with many concurrent users:
1. Use Redis for WebSocket message broadcasting
2. Implement horizontal scaling with multiple server instances
3. Use a WebSocket load balancer (like HAProxy)

---

Happy deploying! 🚀
