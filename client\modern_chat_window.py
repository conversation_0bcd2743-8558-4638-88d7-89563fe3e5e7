import tkinter as tk
from tkinter import ttk, messagebox, filedialog, font
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import os
from PIL import Image, ImageTk
import emoji

from helpers import APIClient, TokenManager, format_timestamp
from websocket_client import WebSocketClient

class ModernChatWindow:
    """Modern WhatsApp-like chat window interface."""
    
    def __init__(self, parent: ttk.Frame, user_info: Dict[str, Any], 
                 api_client: APIClient, token_manager: TokenManager, 
                 on_logout: Callable):
        self.parent = parent
        self.user_info = user_info
        self.api_client = api_client
        self.token_manager = token_manager
        self.on_logout = on_logout
        
        # Modern color scheme (WhatsApp-like)
        self.colors = {
            'primary': '#075E54',      # Dark green
            'secondary': '#128C7E',    # Medium green
            'accent': '#25D366',       # Light green
            'background': '#ECE5DD',   # Light beige
            'chat_bg': '#E5DDD5',      # Chat background
            'sent_msg': '#DCF8C6',     # Sent message bubble
            'received_msg': '#FFFFFF', # Received message bubble
            'sidebar': '#EDEDED',      # Sidebar background
            'text_primary': '#000000', # Primary text
            'text_secondary': '#667781', # Secondary text
            'border': '#D1D7DB',       # Border color
            'online': '#4FC3F7',       # Online indicator
            'typing': '#FFA726'        # Typing indicator
        }
        
        # State
        self.chat_rooms: List[Dict[str, Any]] = []
        self.current_room: Optional[Dict[str, Any]] = None
        self.messages: Dict[int, List[Dict[str, Any]]] = {}
        self.typing_users: Dict[int, set] = {}
        self.online_users: set = set()
        self.dark_mode = False
        
        # WebSocket client
        self.ws_client: Optional[WebSocketClient] = None
        
        # Typing timer
        self.typing_timer: Optional[str] = None
        
        # Fonts
        self.setup_fonts()
        
        self.setup_ui()
        self.load_chat_rooms()
        self.setup_websocket()
    
    def setup_fonts(self):
        """Set up modern fonts."""
        self.fonts = {
            'title': font.Font(family="Segoe UI", size=16, weight="bold"),
            'subtitle': font.Font(family="Segoe UI", size=12, weight="bold"),
            'body': font.Font(family="Segoe UI", size=11),
            'small': font.Font(family="Segoe UI", size=9),
            'emoji': font.Font(family="Segoe UI Emoji", size=11)
        }
    
    def setup_ui(self):
        """Set up the modern chat window UI."""
        # Clear parent frame
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # Configure style
        self.setup_styles()
        
        # Main container with modern layout
        self.main_frame = tk.Frame(self.parent, bg=self.colors['background'])
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(0, weight=1)
        
        self.setup_modern_sidebar()
        self.setup_modern_chat_area()
        self.setup_modern_status_bar()
    
    def setup_styles(self):
        """Set up modern ttk styles."""
        style = ttk.Style()
        
        # Configure modern button style
        style.configure('Modern.TButton',
                       background=self.colors['accent'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(10, 5))
        
        style.map('Modern.TButton',
                 background=[('active', self.colors['secondary'])])
        
        # Configure modern entry style
        style.configure('Modern.TEntry',
                       borderwidth=1,
                       relief='solid',
                       padding=(10, 8))
        
        # Configure modern frame style
        style.configure('Sidebar.TFrame',
                       background=self.colors['sidebar'],
                       borderwidth=1,
                       relief='solid')
    
    def setup_modern_sidebar(self):
        """Set up the modern sidebar with chat list."""
        # Sidebar frame with modern styling
        self.sidebar_frame = tk.Frame(self.main_frame, 
                                     bg=self.colors['sidebar'], 
                                     width=320,
                                     relief='solid',
                                     bd=1)
        self.sidebar_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 1))
        self.sidebar_frame.grid_propagate(False)
        
        # Header with user profile
        self.setup_sidebar_header()
        
        # Search bar
        self.setup_search_bar()
        
        # Chat list
        self.setup_chat_list()
    
    def setup_sidebar_header(self):
        """Set up the sidebar header with user profile."""
        header_frame = tk.Frame(self.sidebar_frame, 
                               bg=self.colors['primary'], 
                               height=60)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 1))
        header_frame.grid_propagate(False)
        header_frame.columnconfigure(1, weight=1)
        
        # User avatar placeholder
        avatar_frame = tk.Frame(header_frame, bg=self.colors['primary'], width=40, height=40)
        avatar_frame.grid(row=0, column=0, padx=15, pady=10)
        avatar_frame.grid_propagate(False)
        
        # Create circular avatar
        self.create_avatar(avatar_frame, self.user_info.get('username', 'U')[0].upper())
        
        # User info
        info_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        info_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=10)
        
        username_label = tk.Label(info_frame, 
                                 text=self.user_info.get('username', 'User'),
                                 bg=self.colors['primary'],
                                 fg='white',
                                 font=self.fonts['subtitle'])
        username_label.grid(row=0, column=0, sticky=tk.W)
        
        status_label = tk.Label(info_frame,
                               text="Online",
                               bg=self.colors['primary'],
                               fg='#B0BEC5',
                               font=self.fonts['small'])
        status_label.grid(row=1, column=0, sticky=tk.W)
        
        # Menu button
        menu_btn = tk.Button(header_frame,
                            text="⋮",
                            bg=self.colors['primary'],
                            fg='white',
                            bd=0,
                            font=self.fonts['title'],
                            command=self.show_menu)
        menu_btn.grid(row=0, column=2, padx=15)
    
    def create_avatar(self, parent, initial):
        """Create a circular avatar with initial."""
        canvas = tk.Canvas(parent, width=40, height=40, 
                          bg=self.colors['primary'], highlightthickness=0)
        canvas.pack(fill=tk.BOTH, expand=True)
        
        # Draw circle
        canvas.create_oval(2, 2, 38, 38, fill=self.colors['accent'], outline='white', width=2)
        canvas.create_text(20, 20, text=initial, fill='white', font=self.fonts['subtitle'])
    
    def setup_search_bar(self):
        """Set up the search bar."""
        search_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'], height=50)
        search_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        search_frame.grid_propagate(False)
        search_frame.columnconfigure(0, weight=1)
        
        # Search entry with modern styling
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame,
                               textvariable=self.search_var,
                               bg='white',
                               fg=self.colors['text_primary'],
                               bd=1,
                               relief='solid',
                               font=self.fonts['body'])
        search_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(30, 5), pady=5)
        
        # Search icon
        search_icon = tk.Label(search_frame,
                              text="🔍",
                              bg=self.colors['sidebar'],
                              font=self.fonts['emoji'])
        search_icon.grid(row=0, column=0, sticky=tk.W, padx=10)
        
        # Placeholder text
        search_entry.insert(0, "Search or start new chat")
        search_entry.bind('<FocusIn>', lambda e: self.clear_placeholder(search_entry))
        search_entry.bind('<KeyRelease>', self.on_search)
    
    def setup_chat_list(self):
        """Set up the chat list with modern styling."""
        # Chat list frame
        list_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'])
        list_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        
        # Scrollable chat list
        self.chat_canvas = tk.Canvas(list_frame, bg=self.colors['sidebar'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.chat_canvas.yview)
        self.chat_scrollable_frame = tk.Frame(self.chat_canvas, bg=self.colors['sidebar'])
        
        self.chat_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.chat_canvas.configure(scrollregion=self.chat_canvas.bbox("all"))
        )
        
        self.chat_canvas.create_window((0, 0), window=self.chat_scrollable_frame, anchor="nw")
        self.chat_canvas.configure(yscrollcommand=scrollbar.set)
        
        self.chat_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        list_frame.rowconfigure(0, weight=1)
        list_frame.columnconfigure(0, weight=1)
    
    def setup_modern_chat_area(self):
        """Set up the modern chat area."""
        # Chat area frame
        self.chat_frame = tk.Frame(self.main_frame, bg=self.colors['chat_bg'])
        self.chat_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.chat_frame.columnconfigure(0, weight=1)
        self.chat_frame.rowconfigure(1, weight=1)
        
        # Chat header
        self.setup_chat_header()
        
        # Messages area
        self.setup_messages_area()
        
        # Message input area
        self.setup_message_input()
    
    def setup_chat_header(self):
        """Set up the chat header."""
        self.chat_header = tk.Frame(self.chat_frame, 
                                   bg=self.colors['primary'], 
                                   height=60)
        self.chat_header.grid(row=0, column=0, sticky=(tk.W, tk.E))
        self.chat_header.grid_propagate(False)
        self.chat_header.columnconfigure(1, weight=1)
        
        # Back button (for mobile-like experience)
        back_btn = tk.Button(self.chat_header,
                            text="←",
                            bg=self.colors['primary'],
                            fg='white',
                            bd=0,
                            font=self.fonts['title'],
                            command=self.go_back)
        back_btn.grid(row=0, column=0, padx=10)
        
        # Chat info
        self.chat_info_frame = tk.Frame(self.chat_header, bg=self.colors['primary'])
        self.chat_info_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=10)
        
        self.chat_name_label = tk.Label(self.chat_info_frame,
                                       text="Select a chat",
                                       bg=self.colors['primary'],
                                       fg='white',
                                       font=self.fonts['subtitle'])
        self.chat_name_label.grid(row=0, column=0, sticky=tk.W)
        
        self.chat_status_label = tk.Label(self.chat_info_frame,
                                         text="",
                                         bg=self.colors['primary'],
                                         fg='#B0BEC5',
                                         font=self.fonts['small'])
        self.chat_status_label.grid(row=1, column=0, sticky=tk.W)
        
        # Action buttons
        actions_frame = tk.Frame(self.chat_header, bg=self.colors['primary'])
        actions_frame.grid(row=0, column=2, padx=10)
        
        # Video call button
        video_btn = tk.Button(actions_frame,
                             text="📹",
                             bg=self.colors['primary'],
                             fg='white',
                             bd=0,
                             font=self.fonts['emoji'],
                             command=self.start_video_call)
        video_btn.grid(row=0, column=0, padx=5)
        
        # Voice call button
        call_btn = tk.Button(actions_frame,
                            text="📞",
                            bg=self.colors['primary'],
                            fg='white',
                            bd=0,
                            font=self.fonts['emoji'],
                            command=self.start_voice_call)
        call_btn.grid(row=0, column=1, padx=5)
        
        # Menu button
        menu_btn = tk.Button(actions_frame,
                            text="⋮",
                            bg=self.colors['primary'],
                            fg='white',
                            bd=0,
                            font=self.fonts['title'],
                            command=self.show_chat_menu)
        menu_btn.grid(row=0, column=2, padx=5)
    
    def setup_messages_area(self):
        """Set up the messages area with modern chat bubbles."""
        # Messages container
        messages_container = tk.Frame(self.chat_frame, bg=self.colors['chat_bg'])
        messages_container.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=5)
        messages_container.columnconfigure(0, weight=1)
        messages_container.rowconfigure(0, weight=1)
        
        # Scrollable messages area
        self.messages_canvas = tk.Canvas(messages_container, 
                                        bg=self.colors['chat_bg'], 
                                        highlightthickness=0)
        messages_scrollbar = ttk.Scrollbar(messages_container, 
                                          orient="vertical", 
                                          command=self.messages_canvas.yview)
        self.messages_scrollable_frame = tk.Frame(self.messages_canvas, bg=self.colors['chat_bg'])
        
        self.messages_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.messages_canvas.configure(scrollregion=self.messages_canvas.bbox("all"))
        )
        
        self.messages_canvas.create_window((0, 0), window=self.messages_scrollable_frame, anchor="nw")
        self.messages_canvas.configure(yscrollcommand=messages_scrollbar.set)
        
        self.messages_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        messages_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Typing indicator
        self.typing_frame = tk.Frame(self.chat_frame, bg=self.colors['chat_bg'], height=30)
        self.typing_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        self.typing_frame.grid_propagate(False)
        
        self.typing_label = tk.Label(self.typing_frame,
                                    text="",
                                    bg=self.colors['chat_bg'],
                                    fg=self.colors['typing'],
                                    font=self.fonts['small'])
        self.typing_label.grid(row=0, column=0, sticky=tk.W, padx=20)
    
    def setup_message_input(self):
        """Set up the modern message input area."""
        input_container = tk.Frame(self.chat_frame, bg=self.colors['background'], height=70)
        input_container.grid(row=3, column=0, sticky=(tk.W, tk.E))
        input_container.grid_propagate(False)
        input_container.columnconfigure(1, weight=1)
        
        # Attachment button
        attach_btn = tk.Button(input_container,
                              text="📎",
                              bg=self.colors['background'],
                              fg=self.colors['text_secondary'],
                              bd=0,
                              font=self.fonts['emoji'],
                              command=self.show_attachment_menu)
        attach_btn.grid(row=0, column=0, padx=10, pady=15)
        
        # Message input with modern styling
        input_frame = tk.Frame(input_container, bg='white', relief='solid', bd=1)
        input_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=15)
        input_frame.columnconfigure(0, weight=1)
        
        self.message_var = tk.StringVar()
        self.message_entry = tk.Entry(input_frame,
                                     textvariable=self.message_var,
                                     bg='white',
                                     fg=self.colors['text_primary'],
                                     bd=0,
                                     font=self.fonts['body'])
        self.message_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=10, pady=10)
        self.message_entry.bind('<Return>', self.send_message)
        self.message_entry.bind('<KeyPress>', self.on_typing)
        
        # Emoji button
        emoji_btn = tk.Button(input_frame,
                             text="😊",
                             bg='white',
                             fg=self.colors['text_secondary'],
                             bd=0,
                             font=self.fonts['emoji'],
                             command=self.show_emoji_picker)
        emoji_btn.grid(row=0, column=1, padx=5)
        
        # Send button
        self.send_btn = tk.Button(input_container,
                                 text="➤",
                                 bg=self.colors['accent'],
                                 fg='white',
                                 bd=0,
                                 font=self.fonts['title'],
                                 width=3,
                                 height=1,
                                 command=self.send_message)
        self.send_btn.grid(row=0, column=2, padx=10, pady=15)
    
    def setup_modern_status_bar(self):
        """Set up the modern status bar."""
        self.status_frame = tk.Frame(self.main_frame, 
                                    bg=self.colors['sidebar'], 
                                    height=25)
        self.status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        self.status_frame.grid_propagate(False)
        
        self.connection_status_label = tk.Label(self.status_frame,
                                               text="Connecting...",
                                               bg=self.colors['sidebar'],
                                               fg=self.colors['text_secondary'],
                                               font=self.fonts['small'])
        self.connection_status_label.grid(row=0, column=0, sticky=tk.W, padx=10)
    
    # Placeholder methods for new features
    def clear_placeholder(self, entry):
        """Clear placeholder text."""
        if entry.get() == "Search or start new chat":
            entry.delete(0, tk.END)
    
    def on_search(self, event=None):
        """Handle search input."""
        # TODO: Implement search functionality
        pass
    
    def show_menu(self):
        """Show main menu."""
        # TODO: Implement menu
        messagebox.showinfo("Menu", "Main menu coming soon!")
    
    def go_back(self):
        """Go back to chat list."""
        # TODO: Implement mobile-like navigation
        pass
    
    def start_video_call(self):
        """Start video call."""
        messagebox.showinfo("Video Call", "Video calling coming soon!")
    
    def start_voice_call(self):
        """Start voice call."""
        messagebox.showinfo("Voice Call", "Voice calling coming soon!")
    
    def show_chat_menu(self):
        """Show chat-specific menu."""
        messagebox.showinfo("Chat Menu", "Chat menu coming soon!")
    
    def show_attachment_menu(self):
        """Show attachment options."""
        messagebox.showinfo("Attachments", "Attachment menu coming soon!")
    
    def show_emoji_picker(self):
        """Show emoji picker."""
        messagebox.showinfo("Emojis", "Emoji picker coming soon!")
    
    def load_chat_rooms(self):
        """Load chat rooms from API."""
        def load_rooms_thread():
            try:
                rooms = self.api_client.get_chat_rooms()
                self.parent.after(0, lambda: self.update_rooms_list(rooms))

                # Also load users for private chats
                users = self.api_client.get_users()
                self.parent.after(0, lambda: self.update_users_list(users))
            except Exception as e:
                self.parent.after(0, lambda: self.show_status(f"Error loading data: {e}"))

        threading.Thread(target=load_rooms_thread, daemon=True).start()

    def update_rooms_list(self, rooms: List[Dict[str, Any]]):
        """Update the rooms list in the UI."""
        self.chat_rooms = rooms
        self.refresh_chat_list()

    def update_users_list(self, users: List[Dict[str, Any]]):
        """Update users list for creating private chats."""
        self.users = [user for user in users if user['id'] != self.user_info['id']]
        self.refresh_chat_list()

    def refresh_chat_list(self):
        """Refresh the chat list display."""
        # Clear existing chat items
        for widget in self.chat_scrollable_frame.winfo_children():
            widget.destroy()

        # Add existing chat rooms
        for room in self.chat_rooms:
            self.create_chat_item(room, is_room=True)

        # Add separator
        if self.chat_rooms and hasattr(self, 'users'):
            separator = tk.Frame(self.chat_scrollable_frame, bg=self.colors['border'], height=1)
            separator.grid(row=len(self.chat_rooms), column=0, sticky=(tk.W, tk.E), pady=5)

        # Add users for private chats
        if hasattr(self, 'users'):
            for i, user in enumerate(self.users):
                self.create_user_item(user, len(self.chat_rooms) + 1 + i)

    def create_chat_item(self, room: Dict[str, Any], is_room: bool = True):
        """Create a modern chat list item."""
        item_frame = tk.Frame(self.chat_scrollable_frame,
                             bg=self.colors['sidebar'],
                             height=70)
        item_frame.grid(row=len(self.chat_scrollable_frame.winfo_children()),
                       column=0, sticky=(tk.W, tk.E), pady=1)
        item_frame.grid_propagate(False)
        item_frame.columnconfigure(1, weight=1)

        # Avatar
        avatar_frame = tk.Frame(item_frame, bg=self.colors['sidebar'], width=50, height=50)
        avatar_frame.grid(row=0, column=0, padx=15, pady=10)
        avatar_frame.grid_propagate(False)

        initial = room['name'][0].upper() if room['name'] else 'R'
        self.create_avatar(avatar_frame, initial)

        # Chat info
        info_frame = tk.Frame(item_frame, bg=self.colors['sidebar'])
        info_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=10)
        info_frame.columnconfigure(0, weight=1)

        # Chat name
        name_label = tk.Label(info_frame,
                             text=room['name'],
                             bg=self.colors['sidebar'],
                             fg=self.colors['text_primary'],
                             font=self.fonts['subtitle'],
                             anchor=tk.W)
        name_label.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # Last message or status
        status_text = f"Members: {room.get('member_count', 0)}"
        if room.get('is_group'):
            status_text = f"Group • {status_text}"

        status_label = tk.Label(info_frame,
                               text=status_text,
                               bg=self.colors['sidebar'],
                               fg=self.colors['text_secondary'],
                               font=self.fonts['small'],
                               anchor=tk.W)
        status_label.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Time
        time_label = tk.Label(item_frame,
                             text="",
                             bg=self.colors['sidebar'],
                             fg=self.colors['text_secondary'],
                             font=self.fonts['small'])
        time_label.grid(row=0, column=2, padx=15, pady=10, sticky=tk.NE)

        # Click handler
        def on_click(event=None):
            self.select_room(room)

        # Bind click events
        for widget in [item_frame, avatar_frame, info_frame, name_label, status_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", lambda e: item_frame.config(bg='#F0F0F0'))
            widget.bind("<Leave>", lambda e: item_frame.config(bg=self.colors['sidebar']))

    def create_user_item(self, user: Dict[str, Any], row: int):
        """Create a user item for starting private chats."""
        item_frame = tk.Frame(self.chat_scrollable_frame,
                             bg=self.colors['sidebar'],
                             height=70)
        item_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=1)
        item_frame.grid_propagate(False)
        item_frame.columnconfigure(1, weight=1)

        # Avatar
        avatar_frame = tk.Frame(item_frame, bg=self.colors['sidebar'], width=50, height=50)
        avatar_frame.grid(row=0, column=0, padx=15, pady=10)
        avatar_frame.grid_propagate(False)

        initial = user['username'][0].upper()
        self.create_avatar(avatar_frame, initial)

        # User info
        info_frame = tk.Frame(item_frame, bg=self.colors['sidebar'])
        info_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=10)
        info_frame.columnconfigure(0, weight=1)

        # Username
        name_label = tk.Label(info_frame,
                             text=user['username'],
                             bg=self.colors['sidebar'],
                             fg=self.colors['text_primary'],
                             font=self.fonts['subtitle'],
                             anchor=tk.W)
        name_label.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # Status
        status_text = "Online" if user.get('is_online', False) else "Offline"
        status_color = self.colors['online'] if user.get('is_online', False) else self.colors['text_secondary']

        status_label = tk.Label(info_frame,
                               text=status_text,
                               bg=self.colors['sidebar'],
                               fg=status_color,
                               font=self.fonts['small'],
                               anchor=tk.W)
        status_label.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Click handler to start private chat
        def on_click(event=None):
            self.start_private_chat_with_user(user)

        # Bind click events
        for widget in [item_frame, avatar_frame, info_frame, name_label, status_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", lambda e: item_frame.config(bg='#F0F0F0'))
            widget.bind("<Leave>", lambda e: item_frame.config(bg=self.colors['sidebar']))

    def start_private_chat_with_user(self, user: Dict[str, Any]):
        """Start a private chat with a user."""
        def create_chat_thread():
            try:
                # Create or find existing private chat room
                room_name = f"Chat with {user['username']}"
                result = self.api_client.create_chat_room(room_name, "", False)

                if result:
                    # Join the room
                    self.api_client.join_chat_room(result['id'])

                    # Invite the other user (this would need backend support)
                    # For now, we'll just select the room
                    self.parent.after(0, lambda: self.select_room(result))
                    self.parent.after(0, lambda: self.show_status(f"Started chat with {user['username']}"))
                else:
                    self.parent.after(0, lambda: self.show_status("Failed to create chat room"))
            except Exception as e:
                self.parent.after(0, lambda: self.show_status(f"Error: {e}"))

        threading.Thread(target=create_chat_thread, daemon=True).start()

    def select_room(self, room: Dict[str, Any]):
        """Select and load a chat room."""
        self.current_room = room

        # Update chat header
        self.chat_name_label.config(text=room['name'])

        status_text = f"{room.get('member_count', 0)} members"
        if not room.get('is_group', False):
            status_text = "Private chat"

        self.chat_status_label.config(text=status_text)

        # Load messages
        self.load_messages(room['id'])

        # Join room via WebSocket
        if self.ws_client:
            self.ws_client.join_room(room['id'])

        self.show_status(f"Joined: {room['name']}")

    def load_messages(self, room_id: int):
        """Load messages for a room."""
        def load_messages_thread():
            try:
                messages = self.api_client.get_messages(room_id)
                self.parent.after(0, lambda: self.display_messages(room_id, messages))
            except Exception as e:
                self.parent.after(0, lambda: self.show_status(f"Error loading messages: {e}"))

        threading.Thread(target=load_messages_thread, daemon=True).start()

    def display_messages(self, room_id: int, messages: List[Dict[str, Any]]):
        """Display messages with modern chat bubbles."""
        if not self.current_room or self.current_room['id'] != room_id:
            return

        # Clear existing messages
        for widget in self.messages_scrollable_frame.winfo_children():
            widget.destroy()

        # Add messages
        for message in messages:
            self.add_message_bubble(message)

        # Scroll to bottom
        self.messages_canvas.update_idletasks()
        self.messages_canvas.yview_moveto(1.0)

    def add_message_bubble(self, message: Dict[str, Any]):
        """Add a modern message bubble."""
        is_own = message.get('sender_id') == self.user_info['id']

        # Message container
        msg_container = tk.Frame(self.messages_scrollable_frame, bg=self.colors['chat_bg'])
        msg_container.grid(row=len(self.messages_scrollable_frame.winfo_children()),
                          column=0, sticky=(tk.W, tk.E), pady=2, padx=10)
        msg_container.columnconfigure(0, weight=1)

        # Message bubble
        bubble_frame = tk.Frame(msg_container, bg=self.colors['chat_bg'])

        if is_own:
            bubble_frame.grid(row=0, column=0, sticky=tk.E, padx=(50, 0))
            bubble_bg = self.colors['sent_msg']
        else:
            bubble_frame.grid(row=0, column=0, sticky=tk.W, padx=(0, 50))
            bubble_bg = self.colors['received_msg']

        # Bubble content
        bubble = tk.Frame(bubble_frame, bg=bubble_bg, relief='solid', bd=1, padx=12, pady=8)
        bubble.pack()

        # Sender name (for group chats)
        if not is_own and self.current_room.get('is_group', False):
            sender_label = tk.Label(bubble,
                                   text=message.get('sender_username', 'Unknown'),
                                   bg=bubble_bg,
                                   fg=self.colors['accent'],
                                   font=self.fonts['small'])
            sender_label.pack(anchor=tk.W)

        # Message content
        content_label = tk.Label(bubble,
                                text=message['content'],
                                bg=bubble_bg,
                                fg=self.colors['text_primary'],
                                font=self.fonts['body'],
                                wraplength=300,
                                justify=tk.LEFT)
        content_label.pack(anchor=tk.W)

        # Timestamp
        timestamp = format_timestamp(message['created_at'])
        time_label = tk.Label(bubble,
                             text=timestamp,
                             bg=bubble_bg,
                             fg=self.colors['text_secondary'],
                             font=self.fonts['small'])
        time_label.pack(anchor=tk.E)

    def send_message(self, event=None):
        """Send a message."""
        if not self.current_room:
            messagebox.showwarning("No Chat", "Please select a chat room first")
            return

        content = self.message_var.get().strip()
        if not content:
            return

        # Clear input
        self.message_var.set("")

        # Send message via API
        def send_message_thread():
            try:
                result = self.api_client.send_message(self.current_room['id'], content)
                if not result:
                    self.parent.after(0, lambda: self.show_status("Failed to send message"))
            except Exception as e:
                self.parent.after(0, lambda: self.show_status(f"Error: {e}"))

        threading.Thread(target=send_message_thread, daemon=True).start()

    def setup_websocket(self):
        """Set up WebSocket connection."""
        token = self.token_manager.get_access_token()
        if token:
            self.ws_client = WebSocketClient(token, self.handle_websocket_message)
            self.ws_client.start()
            self.update_connection_status()

    def update_connection_status(self):
        """Update connection status display."""
        if self.ws_client:
            status = self.ws_client.connection_status
            if status == "Connected":
                self.connection_status_label.config(text="Connected", fg=self.colors['online'])
            elif "Connecting" in status:
                self.connection_status_label.config(text=status, fg=self.colors['typing'])
            else:
                self.connection_status_label.config(text=status, fg='red')

        # Schedule next update
        self.parent.after(2000, self.update_connection_status)

    def handle_websocket_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket message."""
        try:
            message_type = data.get('type')

            if message_type == 'message':
                self.handle_new_message(data.get('data', {}))
            elif message_type == 'typing_start':
                self.handle_typing_start(data)
            elif message_type == 'typing_stop':
                self.handle_typing_stop(data)
            elif message_type == 'user_online':
                self.handle_user_online(data)
            elif message_type == 'user_offline':
                self.handle_user_offline(data)
        except Exception as e:
            print(f"WebSocket message error: {e}")

    def handle_new_message(self, message_data: Dict[str, Any]):
        """Handle new message from WebSocket."""
        if self.current_room and message_data.get('chat_room_id') == self.current_room['id']:
            self.parent.after(0, lambda: self.add_message_bubble(message_data))

    def handle_typing_start(self, data: Dict[str, Any]):
        """Handle typing indicator."""
        if self.current_room and data.get('room_id') == self.current_room['id']:
            username = data.get('username', 'Someone')
            if username != self.user_info['username']:
                self.parent.after(0, lambda: self.typing_label.config(text=f"{username} is typing..."))

    def handle_typing_stop(self, data: Dict[str, Any]):
        """Handle typing stop."""
        if self.current_room and data.get('room_id') == self.current_room['id']:
            self.parent.after(0, lambda: self.typing_label.config(text=""))

    def handle_user_online(self, data: Dict[str, Any]):
        """Handle user coming online."""
        self.online_users.add(data.get('username'))
        self.refresh_chat_list()

    def handle_user_offline(self, data: Dict[str, Any]):
        """Handle user going offline."""
        self.online_users.discard(data.get('username'))
        self.refresh_chat_list()

    def on_typing(self, event=None):
        """Handle typing indicator."""
        if self.current_room and self.ws_client:
            self.ws_client.start_typing(self.current_room['id'])

    def show_status(self, message: str):
        """Show status message."""
        print(f"Status: {message}")  # For now, just print

    def cleanup(self):
        """Clean up resources."""
        if self.ws_client:
            self.ws_client.stop()

    def destroy(self):
        """Destroy the window."""
        self.cleanup()
        for widget in self.parent.winfo_children():
            widget.destroy()
    
    def load_chat_rooms(self):
        """Load chat rooms."""
        # TODO: Implement chat room loading
        pass
    
    def setup_websocket(self):
        """Set up WebSocket connection."""
        # TODO: Implement WebSocket setup
        pass
    
    def cleanup(self):
        """Clean up resources."""
        if self.ws_client:
            self.ws_client.stop()
    
    def destroy(self):
        """Destroy the chat window."""
        self.cleanup()
        for widget in self.parent.winfo_children():
            widget.destroy()
