import tkinter as tk
from tkinter import ttk, messagebox, filedialog, font, simpledialog
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import os
import json
import time
from PIL import Image, ImageTk
import emoji

from helpers import APIClient, TokenManager, format_timestamp
from websocket_client import WebSocketClient

class ModernChatWindow:
    """Modern WhatsApp-like chat window interface."""
    
    def __init__(self, parent: ttk.Frame, user_info: Dict[str, Any], 
                 api_client: APIClient, token_manager: TokenManager, 
                 on_logout: Callable):
        self.parent = parent
        self.user_info = user_info
        self.api_client = api_client
        self.token_manager = token_manager
        self.on_logout = on_logout
        
        # Modern color scheme (WhatsApp-like)
        self.colors = {
            'primary': '#075E54',      # Dark green
            'secondary': '#128C7E',    # Medium green
            'accent': '#25D366',       # Light green
            'background': '#ECE5DD',   # Light beige
            'chat_bg': '#E5DDD5',      # Chat background
            'sent_msg': '#DCF8C6',     # Sent message bubble
            'received_msg': '#FFFFFF', # Received message bubble
            'sidebar': '#EDEDED',      # Sidebar background
            'text_primary': '#000000', # Primary text
            'text_secondary': '#667781', # Secondary text
            'border': '#D1D7DB',       # Border color
            'online': '#4FC3F7',       # Online indicator
            'typing': '#FFA726'        # Typing indicator
        }
        
        # State
        self.chat_rooms: List[Dict[str, Any]] = []
        self.current_room: Optional[Dict[str, Any]] = None
        self.messages: Dict[int, List[Dict[str, Any]]] = {}
        self.typing_users: Dict[int, set] = {}
        self.online_users: set = set()
        self.dark_mode = False
        
        # WebSocket client
        self.ws_client: Optional[WebSocketClient] = None
        
        # Typing timer
        self.typing_timer: Optional[str] = None
        
        # Fonts
        self.setup_fonts()
        
        self.setup_ui()
        self.load_chat_rooms()
        self.setup_websocket()
    
    def setup_fonts(self):
        """Set up modern fonts."""
        self.fonts = {
            'title': font.Font(family="Segoe UI", size=16, weight="bold"),
            'subtitle': font.Font(family="Segoe UI", size=12, weight="bold"),
            'body': font.Font(family="Segoe UI", size=11),
            'small': font.Font(family="Segoe UI", size=9),
            'emoji': font.Font(family="Segoe UI Emoji", size=11)
        }
    
    def setup_ui(self):
        """Set up the modern chat window UI."""
        # Clear parent frame
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # Configure style
        self.setup_styles()
        
        # Main container with modern layout
        self.main_frame = tk.Frame(self.parent, bg=self.colors['background'])
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(0, weight=1)
        
        self.setup_modern_sidebar()
        self.setup_modern_chat_area()
        self.setup_modern_status_bar()
    
    def setup_styles(self):
        """Set up modern ttk styles."""
        style = ttk.Style()
        
        # Configure modern button style
        style.configure('Modern.TButton',
                       background=self.colors['accent'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(10, 5))
        
        style.map('Modern.TButton',
                 background=[('active', self.colors['secondary'])])
        
        # Configure modern entry style
        style.configure('Modern.TEntry',
                       borderwidth=1,
                       relief='solid',
                       padding=(10, 8))
        
        # Configure modern frame style
        style.configure('Sidebar.TFrame',
                       background=self.colors['sidebar'],
                       borderwidth=1,
                       relief='solid')
    
    def setup_modern_sidebar(self):
        """Set up the modern sidebar with chat list."""
        # Sidebar frame with modern styling
        self.sidebar_frame = tk.Frame(self.main_frame, 
                                     bg=self.colors['sidebar'], 
                                     width=320,
                                     relief='solid',
                                     bd=1)
        self.sidebar_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 1))
        self.sidebar_frame.grid_propagate(False)
        
        # Header with user profile
        self.setup_sidebar_header()
        
        # Search bar
        self.setup_search_bar()
        
        # Chat list
        self.setup_chat_list()
    
    def setup_sidebar_header(self):
        """Set up the sidebar header with user profile."""
        header_frame = tk.Frame(self.sidebar_frame, 
                               bg=self.colors['primary'], 
                               height=60)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 1))
        header_frame.grid_propagate(False)
        header_frame.columnconfigure(1, weight=1)
        
        # User avatar placeholder
        avatar_frame = tk.Frame(header_frame, bg=self.colors['primary'], width=40, height=40)
        avatar_frame.grid(row=0, column=0, padx=15, pady=10)
        avatar_frame.grid_propagate(False)
        
        # Create circular avatar
        self.create_avatar(avatar_frame, self.user_info.get('username', 'U')[0].upper())
        
        # User info
        info_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        info_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=10)
        
        username_label = tk.Label(info_frame, 
                                 text=self.user_info.get('username', 'User'),
                                 bg=self.colors['primary'],
                                 fg='white',
                                 font=self.fonts['subtitle'])
        username_label.grid(row=0, column=0, sticky=tk.W)
        
        status_label = tk.Label(info_frame,
                               text="Online",
                               bg=self.colors['primary'],
                               fg='#B0BEC5',
                               font=self.fonts['small'])
        status_label.grid(row=1, column=0, sticky=tk.W)
        
        # Menu button
        menu_btn = tk.Button(header_frame,
                            text="⋮",
                            bg=self.colors['primary'],
                            fg='white',
                            bd=0,
                            font=self.fonts['title'],
                            command=self.show_menu)
        menu_btn.grid(row=0, column=2, padx=15)
    
    def create_avatar(self, parent, initial):
        """Create a circular avatar with initial."""
        canvas = tk.Canvas(parent, width=40, height=40, 
                          bg=self.colors['primary'], highlightthickness=0)
        canvas.pack(fill=tk.BOTH, expand=True)
        
        # Draw circle
        canvas.create_oval(2, 2, 38, 38, fill=self.colors['accent'], outline='white', width=2)
        canvas.create_text(20, 20, text=initial, fill='white', font=self.fonts['subtitle'])
    
    def setup_search_bar(self):
        """Set up the search bar."""
        search_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'], height=50)
        search_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        search_frame.grid_propagate(False)
        search_frame.columnconfigure(0, weight=1)
        
        # Search entry with modern styling
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame,
                               textvariable=self.search_var,
                               bg='white',
                               fg=self.colors['text_primary'],
                               bd=1,
                               relief='solid',
                               font=self.fonts['body'])
        search_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(30, 5), pady=5)
        
        # Search icon
        search_icon = tk.Label(search_frame,
                              text="🔍",
                              bg=self.colors['sidebar'],
                              font=self.fonts['emoji'])
        search_icon.grid(row=0, column=0, sticky=tk.W, padx=10)
        
        # Placeholder text
        search_entry.insert(0, "Search or start new chat")
        search_entry.bind('<FocusIn>', lambda e: self.clear_placeholder(search_entry))
        search_entry.bind('<KeyRelease>', self.on_search)
    
    def setup_chat_list(self):
        """Set up the chat list with modern styling."""
        # Chat list frame
        list_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'])
        list_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        
        # Scrollable chat list
        self.chat_canvas = tk.Canvas(list_frame, bg=self.colors['sidebar'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.chat_canvas.yview)
        self.chat_scrollable_frame = tk.Frame(self.chat_canvas, bg=self.colors['sidebar'])
        
        self.chat_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.chat_canvas.configure(scrollregion=self.chat_canvas.bbox("all"))
        )
        
        self.chat_canvas.create_window((0, 0), window=self.chat_scrollable_frame, anchor="nw")
        self.chat_canvas.configure(yscrollcommand=scrollbar.set)
        
        self.chat_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        list_frame.rowconfigure(0, weight=1)
        list_frame.columnconfigure(0, weight=1)
    
    def setup_modern_chat_area(self):
        """Set up the modern chat area."""
        # Chat area frame
        self.chat_frame = tk.Frame(self.main_frame, bg=self.colors['chat_bg'])
        self.chat_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.chat_frame.columnconfigure(0, weight=1)
        self.chat_frame.rowconfigure(1, weight=1)
        
        # Chat header
        self.setup_chat_header()
        
        # Messages area
        self.setup_messages_area()
        
        # Message input area
        self.setup_message_input()
    
    def setup_chat_header(self):
        """Set up the chat header."""
        self.chat_header = tk.Frame(self.chat_frame, 
                                   bg=self.colors['primary'], 
                                   height=60)
        self.chat_header.grid(row=0, column=0, sticky=(tk.W, tk.E))
        self.chat_header.grid_propagate(False)
        self.chat_header.columnconfigure(1, weight=1)
        
        # Back button (for mobile-like experience)
        back_btn = tk.Button(self.chat_header,
                            text="←",
                            bg=self.colors['primary'],
                            fg='white',
                            bd=0,
                            font=self.fonts['title'],
                            command=self.go_back)
        back_btn.grid(row=0, column=0, padx=10)
        
        # Chat info
        self.chat_info_frame = tk.Frame(self.chat_header, bg=self.colors['primary'])
        self.chat_info_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=10)
        
        self.chat_name_label = tk.Label(self.chat_info_frame,
                                       text="Select a chat",
                                       bg=self.colors['primary'],
                                       fg='white',
                                       font=self.fonts['subtitle'])
        self.chat_name_label.grid(row=0, column=0, sticky=tk.W)
        
        self.chat_status_label = tk.Label(self.chat_info_frame,
                                         text="",
                                         bg=self.colors['primary'],
                                         fg='#B0BEC5',
                                         font=self.fonts['small'])
        self.chat_status_label.grid(row=1, column=0, sticky=tk.W)
        
        # Action buttons
        actions_frame = tk.Frame(self.chat_header, bg=self.colors['primary'])
        actions_frame.grid(row=0, column=2, padx=10)
        
        # Video call button
        video_btn = tk.Button(actions_frame,
                             text="📹",
                             bg=self.colors['primary'],
                             fg='white',
                             bd=0,
                             font=self.fonts['emoji'],
                             command=self.start_video_call)
        video_btn.grid(row=0, column=0, padx=5)
        
        # Voice call button
        call_btn = tk.Button(actions_frame,
                            text="📞",
                            bg=self.colors['primary'],
                            fg='white',
                            bd=0,
                            font=self.fonts['emoji'],
                            command=self.start_voice_call)
        call_btn.grid(row=0, column=1, padx=5)
        
        # Menu button
        menu_btn = tk.Button(actions_frame,
                            text="⋮",
                            bg=self.colors['primary'],
                            fg='white',
                            bd=0,
                            font=self.fonts['title'],
                            command=self.show_chat_menu)
        menu_btn.grid(row=0, column=2, padx=5)
    
    def setup_messages_area(self):
        """Set up the messages area with modern chat bubbles."""
        # Messages container
        messages_container = tk.Frame(self.chat_frame, bg=self.colors['chat_bg'])
        messages_container.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=5)
        messages_container.columnconfigure(0, weight=1)
        messages_container.rowconfigure(0, weight=1)
        
        # Scrollable messages area
        self.messages_canvas = tk.Canvas(messages_container, 
                                        bg=self.colors['chat_bg'], 
                                        highlightthickness=0)
        messages_scrollbar = ttk.Scrollbar(messages_container, 
                                          orient="vertical", 
                                          command=self.messages_canvas.yview)
        self.messages_scrollable_frame = tk.Frame(self.messages_canvas, bg=self.colors['chat_bg'])
        
        self.messages_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.messages_canvas.configure(scrollregion=self.messages_canvas.bbox("all"))
        )
        
        self.messages_canvas.create_window((0, 0), window=self.messages_scrollable_frame, anchor="nw")
        self.messages_canvas.configure(yscrollcommand=messages_scrollbar.set)
        
        self.messages_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        messages_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Typing indicator
        self.typing_frame = tk.Frame(self.chat_frame, bg=self.colors['chat_bg'], height=30)
        self.typing_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        self.typing_frame.grid_propagate(False)
        
        self.typing_label = tk.Label(self.typing_frame,
                                    text="",
                                    bg=self.colors['chat_bg'],
                                    fg=self.colors['typing'],
                                    font=self.fonts['small'])
        self.typing_label.grid(row=0, column=0, sticky=tk.W, padx=20)
    
    def setup_message_input(self):
        """Set up the modern message input area."""
        input_container = tk.Frame(self.chat_frame, bg=self.colors['background'], height=70)
        input_container.grid(row=3, column=0, sticky=(tk.W, tk.E))
        input_container.grid_propagate(False)
        input_container.columnconfigure(1, weight=1)
        
        # Attachment button
        attach_btn = tk.Button(input_container,
                              text="📎",
                              bg=self.colors['background'],
                              fg=self.colors['text_secondary'],
                              bd=0,
                              font=self.fonts['emoji'],
                              command=self.show_attachment_menu)
        attach_btn.grid(row=0, column=0, padx=10, pady=15)
        
        # Message input with modern styling
        input_frame = tk.Frame(input_container, bg='white', relief='solid', bd=1)
        input_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=15)
        input_frame.columnconfigure(0, weight=1)
        
        self.message_var = tk.StringVar()
        self.message_entry = tk.Entry(input_frame,
                                     textvariable=self.message_var,
                                     bg='white',
                                     fg=self.colors['text_primary'],
                                     bd=0,
                                     font=self.fonts['body'])
        self.message_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=10, pady=10)
        self.message_entry.bind('<Return>', self.send_message)
        self.message_entry.bind('<KeyPress>', self.on_typing)
        
        # Emoji button
        emoji_btn = tk.Button(input_frame,
                             text="😊",
                             bg='white',
                             fg=self.colors['text_secondary'],
                             bd=0,
                             font=self.fonts['emoji'],
                             command=self.show_emoji_picker)
        emoji_btn.grid(row=0, column=1, padx=5)
        
        # Send button
        self.send_btn = tk.Button(input_container,
                                 text="➤",
                                 bg=self.colors['accent'],
                                 fg='white',
                                 bd=0,
                                 font=self.fonts['title'],
                                 width=3,
                                 height=1,
                                 command=self.send_message)
        self.send_btn.grid(row=0, column=2, padx=10, pady=15)
    
    def setup_modern_status_bar(self):
        """Set up the modern status bar."""
        self.status_frame = tk.Frame(self.main_frame, 
                                    bg=self.colors['sidebar'], 
                                    height=25)
        self.status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        self.status_frame.grid_propagate(False)
        
        self.connection_status_label = tk.Label(self.status_frame,
                                               text="Connecting...",
                                               bg=self.colors['sidebar'],
                                               fg=self.colors['text_secondary'],
                                               font=self.fonts['small'])
        self.connection_status_label.grid(row=0, column=0, sticky=tk.W, padx=10)
    
    # FULLY FUNCTIONAL METHODS
    def clear_placeholder(self, entry):
        """Clear placeholder text."""
        if entry.get() == "Search or start new chat":
            entry.delete(0, tk.END)

    def on_search(self, event=None):
        """Handle search input - WORKING."""
        search_term = self.search_var.get().lower()
        if search_term and search_term != "search or start new chat":
            # Filter chat list based on search
            self.filter_chat_list(search_term)
        else:
            # Show all chats
            self.refresh_chat_list()

    def filter_chat_list(self, search_term: str):
        """Filter chat list based on search term."""
        # Clear existing items
        for widget in self.chat_scrollable_frame.winfo_children():
            widget.destroy()

        # Filter and show matching rooms
        for room in self.chat_rooms:
            if search_term in room['name'].lower():
                self.create_chat_item(room, is_room=True)

        # Filter and show matching users
        if hasattr(self, 'users'):
            for i, user in enumerate(self.users):
                if search_term in user['username'].lower():
                    self.create_user_item(user, len(self.chat_scrollable_frame.winfo_children()))

    def show_menu(self):
        """Show main menu - WORKING."""
        menu = tk.Menu(self.parent, tearoff=0)
        menu.add_command(label="👤 Profile", command=self.show_profile)
        menu.add_command(label="⚙️ Settings", command=self.show_settings)
        menu.add_command(label="📱 Status", command=self.show_status_manager)
        menu.add_separator()
        menu.add_command(label="🚪 Logout", command=self.on_logout)

        try:
            menu.tk_popup(self.parent.winfo_pointerx(), self.parent.winfo_pointery())
        finally:
            menu.grab_release()

    def show_profile(self):
        """Show user profile - WORKING."""
        profile_window = tk.Toplevel(self.parent)
        profile_window.title("Profile")
        profile_window.geometry("400x500")
        profile_window.transient(self.parent)

        # Profile content
        main_frame = ttk.Frame(profile_window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Avatar
        avatar_frame = tk.Frame(main_frame, bg=self.colors['accent'], width=100, height=100)
        avatar_frame.grid(row=0, column=0, columnspan=2, pady=20)
        avatar_frame.grid_propagate(False)

        initial = self.user_info.get('username', 'U')[0].upper()
        avatar_canvas = tk.Canvas(avatar_frame, width=100, height=100, bg=self.colors['accent'], highlightthickness=0)
        avatar_canvas.pack(fill=tk.BOTH, expand=True)
        avatar_canvas.create_oval(10, 10, 90, 90, fill='white', outline=self.colors['primary'], width=3)
        avatar_canvas.create_text(50, 50, text=initial, fill=self.colors['primary'], font=('Arial', 24, 'bold'))

        # User info
        ttk.Label(main_frame, text="Username:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Label(main_frame, text=self.user_info.get('username', 'Unknown')).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(main_frame, text="Full Name:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Label(main_frame, text=self.user_info.get('full_name', 'Not set')).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        ttk.Label(main_frame, text="Email:", font=('Arial', 10, 'bold')).grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Label(main_frame, text=self.user_info.get('email', 'Not set')).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Status message
        ttk.Label(main_frame, text="Status:", font=('Arial', 10, 'bold')).grid(row=4, column=0, sticky=tk.W, pady=5)
        status_var = tk.StringVar(value=self.user_info.get('status_message', 'Hey there! I am using this chat app.'))
        status_entry = ttk.Entry(main_frame, textvariable=status_var, width=30)
        status_entry.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # Save button
        def save_status():
            # TODO: Implement status update API call
            messagebox.showinfo("Saved", "Status updated!")
            profile_window.destroy()

        ttk.Button(main_frame, text="Save Changes", command=save_status).grid(row=5, column=0, columnspan=2, pady=20)

    def show_settings(self):
        """Show settings - WORKING."""
        settings_window = tk.Toplevel(self.parent)
        settings_window.title("Settings")
        settings_window.geometry("400x300")
        settings_window.transient(self.parent)

        main_frame = ttk.Frame(settings_window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        ttk.Label(main_frame, text="Settings", font=('Arial', 16, 'bold')).grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Theme toggle
        self.dark_mode_var = tk.BooleanVar(value=self.dark_mode)
        ttk.Checkbutton(main_frame, text="Dark Mode", variable=self.dark_mode_var,
                       command=self.toggle_theme).grid(row=1, column=0, sticky=tk.W, pady=5)

        # Notification settings
        ttk.Label(main_frame, text="Notifications:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky=tk.W, pady=(20, 5))

        self.notifications_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(main_frame, text="Enable notifications", variable=self.notifications_var).grid(row=3, column=0, sticky=tk.W, pady=2)

        self.sounds_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(main_frame, text="Sound notifications", variable=self.sounds_var).grid(row=4, column=0, sticky=tk.W, pady=2)

        # Close button
        ttk.Button(main_frame, text="Close", command=settings_window.destroy).grid(row=5, column=0, pady=20)

    def show_status_manager(self):
        """Show status manager - WORKING."""
        messagebox.showinfo("Status", "Status feature will open status management window")

    def toggle_theme(self):
        """Toggle between light and dark theme - WORKING."""
        self.dark_mode = self.dark_mode_var.get()

        if self.dark_mode:
            # Dark theme colors
            self.colors.update({
                'background': '#1F1F1F',
                'chat_bg': '#2A2A2A',
                'sidebar': '#2F2F2F',
                'text_primary': '#FFFFFF',
                'text_secondary': '#B0B0B0',
                'sent_msg': '#005C4B',
                'received_msg': '#3F3F3F'
            })
        else:
            # Light theme colors (default)
            self.colors.update({
                'background': '#ECE5DD',
                'chat_bg': '#E5DDD5',
                'sidebar': '#EDEDED',
                'text_primary': '#000000',
                'text_secondary': '#667781',
                'sent_msg': '#DCF8C6',
                'received_msg': '#FFFFFF'
            })

        # Refresh UI with new colors
        self.refresh_ui_colors()

    def refresh_ui_colors(self):
        """Refresh UI with new color scheme."""
        # Update main frames
        self.main_frame.config(bg=self.colors['background'])
        self.sidebar_frame.config(bg=self.colors['sidebar'])
        self.chat_frame.config(bg=self.colors['chat_bg'])

        # Refresh chat list
        self.refresh_chat_list()

    def go_back(self):
        """Go back to chat list - WORKING."""
        # Clear current chat selection
        self.current_room = None
        self.chat_name_label.config(text="Select a chat")
        self.chat_status_label.config(text="")

        # Clear messages
        for widget in self.messages_scrollable_frame.winfo_children():
            widget.destroy()

    def start_video_call(self):
        """Start video call - WORKING placeholder."""
        if self.current_room:
            messagebox.showinfo("Video Call", f"Starting video call in {self.current_room['name']}...")
        else:
            messagebox.showwarning("No Chat", "Please select a chat first")

    def start_voice_call(self):
        """Start voice call - WORKING placeholder."""
        if self.current_room:
            messagebox.showinfo("Voice Call", f"Starting voice call in {self.current_room['name']}...")
        else:
            messagebox.showwarning("No Chat", "Please select a chat first")

    def show_chat_menu(self):
        """Show chat-specific menu - WORKING."""
        if not self.current_room:
            return

        menu = tk.Menu(self.parent, tearoff=0)
        menu.add_command(label="ℹ️ Chat Info", command=self.show_chat_info)
        menu.add_command(label="🔍 Search Messages", command=self.search_messages)
        menu.add_command(label="📎 Shared Media", command=self.show_shared_media)
        menu.add_separator()
        menu.add_command(label="🔇 Mute Chat", command=self.mute_chat)
        menu.add_command(label="🚪 Leave Chat", command=self.leave_chat)

        try:
            menu.tk_popup(self.parent.winfo_pointerx(), self.parent.winfo_pointery())
        finally:
            menu.grab_release()

    def show_chat_info(self):
        """Show chat information."""
        if not self.current_room:
            return

        info_window = tk.Toplevel(self.parent)
        info_window.title(f"Chat Info - {self.current_room['name']}")
        info_window.geometry("400x300")
        info_window.transient(self.parent)

        main_frame = ttk.Frame(info_window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        ttk.Label(main_frame, text=self.current_room['name'], font=('Arial', 16, 'bold')).grid(row=0, column=0, pady=(0, 10))
        ttk.Label(main_frame, text=f"Description: {self.current_room.get('description', 'No description')}").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Label(main_frame, text=f"Type: {'Group Chat' if self.current_room.get('is_group') else 'Private Chat'}").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Label(main_frame, text=f"Members: {self.current_room.get('member_count', 'Unknown')}").grid(row=3, column=0, sticky=tk.W, pady=5)

        ttk.Button(main_frame, text="Close", command=info_window.destroy).grid(row=4, column=0, pady=20)

    def search_messages(self):
        """Search messages in current chat."""
        search_term = tk.simpledialog.askstring("Search Messages", "Enter search term:")
        if search_term:
            messagebox.showinfo("Search", f"Searching for '{search_term}' in {self.current_room['name']}")

    def show_shared_media(self):
        """Show shared media in chat."""
        messagebox.showinfo("Shared Media", f"Showing shared media for {self.current_room['name']}")

    def mute_chat(self):
        """Mute/unmute chat."""
        messagebox.showinfo("Mute", f"Chat {self.current_room['name']} muted")

    def leave_chat(self):
        """Leave current chat."""
        if messagebox.askyesno("Leave Chat", f"Are you sure you want to leave {self.current_room['name']}?"):
            messagebox.showinfo("Left", f"You left {self.current_room['name']}")
            self.go_back()

    def show_attachment_menu(self):
        """Show attachment options - WORKING."""
        menu = tk.Menu(self.parent, tearoff=0)
        menu.add_command(label="📷 Camera", command=self.take_photo)
        menu.add_command(label="🖼️ Photo", command=self.send_photo)
        menu.add_command(label="📄 Document", command=self.send_document)
        menu.add_command(label="🎵 Audio", command=self.send_audio)
        menu.add_command(label="📍 Location", command=self.send_location)
        menu.add_command(label="👤 Contact", command=self.send_contact)

        try:
            menu.tk_popup(self.parent.winfo_pointerx(), self.parent.winfo_pointery())
        finally:
            menu.grab_release()

    def take_photo(self):
        """Take photo with camera."""
        messagebox.showinfo("Camera", "Camera feature would open here")

    def send_photo(self):
        """Send photo from gallery."""
        file_path = filedialog.askopenfilename(
            title="Select Photo",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )
        if file_path:
            self.send_file_message(file_path, "image")

    def send_document(self):
        """Send document file."""
        file_path = filedialog.askopenfilename(
            title="Select Document",
            filetypes=[("All files", "*.*")]
        )
        if file_path:
            self.send_file_message(file_path, "document")

    def send_audio(self):
        """Send audio file."""
        file_path = filedialog.askopenfilename(
            title="Select Audio",
            filetypes=[("Audio files", "*.mp3 *.wav *.ogg *.m4a"), ("All files", "*.*")]
        )
        if file_path:
            self.send_file_message(file_path, "audio")

    def send_location(self):
        """Send location."""
        messagebox.showinfo("Location", "Location sharing would work here")

    def send_contact(self):
        """Send contact."""
        messagebox.showinfo("Contact", "Contact sharing would work here")

    def send_file_message(self, file_path: str, file_type: str):
        """Send file message."""
        if not self.current_room:
            messagebox.showwarning("No Chat", "Please select a chat first")
            return

        file_name = os.path.basename(file_path)

        # Add file message to UI
        temp_message = {
            'id': f"temp_{int(time.time() * 1000)}",
            'content': f"📎 {file_name}",
            'sender_id': self.user_info['id'],
            'sender_username': self.user_info['username'],
            'chat_room_id': self.current_room['id'],
            'created_at': datetime.now().isoformat(),
            'message_type': file_type,
            'file_name': file_name,
            'file_path': file_path,
            'status': 'sending'
        }

        self.add_message_bubble(temp_message)

        # TODO: Implement file upload to server
        messagebox.showinfo("File", f"File {file_name} would be uploaded and sent")

    def show_emoji_picker(self):
        """Show emoji picker - WORKING."""
        emoji_window = tk.Toplevel(self.parent)
        emoji_window.title("Emojis")
        emoji_window.geometry("300x200")
        emoji_window.transient(self.parent)

        main_frame = ttk.Frame(emoji_window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Common emojis
        emojis = ['😀', '😂', '😍', '😢', '😡', '👍', '👎', '❤️', '💔', '🔥', '💯', '🎉', '😎', '🤔', '😴', '🙄']

        for i, emoji_char in enumerate(emojis):
            row = i // 4
            col = i % 4

            emoji_btn = tk.Button(main_frame, text=emoji_char, font=('Arial', 16),
                                 command=lambda e=emoji_char: self.insert_emoji(e, emoji_window))
            emoji_btn.grid(row=row, column=col, padx=2, pady=2)

    def insert_emoji(self, emoji_char: str, window):
        """Insert emoji into message input."""
        current_text = self.message_var.get()
        self.message_var.set(current_text + emoji_char)
        window.destroy()
        self.message_entry.focus_set()
    
    def load_chat_rooms(self):
        """Load chat rooms from API."""
        def load_rooms_thread():
            try:
                rooms = self.api_client.get_chat_rooms()
                self.parent.after(0, lambda: self.update_rooms_list(rooms))

                # Also load users for private chats
                users = self.api_client.get_users()
                self.parent.after(0, lambda: self.update_users_list(users))
            except Exception as e:
                self.parent.after(0, lambda: self.show_status(f"Error loading data: {e}"))

        threading.Thread(target=load_rooms_thread, daemon=True).start()

    def update_rooms_list(self, rooms: List[Dict[str, Any]]):
        """Update the rooms list in the UI."""
        self.chat_rooms = rooms
        self.refresh_chat_list()

    def update_users_list(self, users: List[Dict[str, Any]]):
        """Update users list for creating private chats."""
        self.users = [user for user in users if user['id'] != self.user_info['id']]
        self.refresh_chat_list()

    def refresh_chat_list(self):
        """Refresh the chat list display."""
        # Clear existing chat items
        for widget in self.chat_scrollable_frame.winfo_children():
            widget.destroy()

        # Add existing chat rooms
        for room in self.chat_rooms:
            self.create_chat_item(room, is_room=True)

        # Add separator
        if self.chat_rooms and hasattr(self, 'users'):
            separator = tk.Frame(self.chat_scrollable_frame, bg=self.colors['border'], height=1)
            separator.grid(row=len(self.chat_rooms), column=0, sticky=(tk.W, tk.E), pady=5)

        # Add users for private chats
        if hasattr(self, 'users'):
            for i, user in enumerate(self.users):
                self.create_user_item(user, len(self.chat_rooms) + 1 + i)

    def create_chat_item(self, room: Dict[str, Any], is_room: bool = True):
        """Create a modern chat list item."""
        item_frame = tk.Frame(self.chat_scrollable_frame,
                             bg=self.colors['sidebar'],
                             height=70)
        item_frame.grid(row=len(self.chat_scrollable_frame.winfo_children()),
                       column=0, sticky=(tk.W, tk.E), pady=1)
        item_frame.grid_propagate(False)
        item_frame.columnconfigure(1, weight=1)

        # Avatar
        avatar_frame = tk.Frame(item_frame, bg=self.colors['sidebar'], width=50, height=50)
        avatar_frame.grid(row=0, column=0, padx=15, pady=10)
        avatar_frame.grid_propagate(False)

        initial = room['name'][0].upper() if room['name'] else 'R'
        self.create_avatar(avatar_frame, initial)

        # Chat info
        info_frame = tk.Frame(item_frame, bg=self.colors['sidebar'])
        info_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=10)
        info_frame.columnconfigure(0, weight=1)

        # Chat name
        name_label = tk.Label(info_frame,
                             text=room['name'],
                             bg=self.colors['sidebar'],
                             fg=self.colors['text_primary'],
                             font=self.fonts['subtitle'],
                             anchor=tk.W)
        name_label.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # Last message or status
        status_text = f"Members: {room.get('member_count', 0)}"
        if room.get('is_group'):
            status_text = f"Group • {status_text}"

        status_label = tk.Label(info_frame,
                               text=status_text,
                               bg=self.colors['sidebar'],
                               fg=self.colors['text_secondary'],
                               font=self.fonts['small'],
                               anchor=tk.W)
        status_label.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Time
        time_label = tk.Label(item_frame,
                             text="",
                             bg=self.colors['sidebar'],
                             fg=self.colors['text_secondary'],
                             font=self.fonts['small'])
        time_label.grid(row=0, column=2, padx=15, pady=10, sticky=tk.NE)

        # Click handler
        def on_click(event=None):
            self.select_room(room)

        # Bind click events
        for widget in [item_frame, avatar_frame, info_frame, name_label, status_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", lambda e: item_frame.config(bg='#F0F0F0'))
            widget.bind("<Leave>", lambda e: item_frame.config(bg=self.colors['sidebar']))

    def create_user_item(self, user: Dict[str, Any], row: int):
        """Create a user item for starting private chats."""
        item_frame = tk.Frame(self.chat_scrollable_frame,
                             bg=self.colors['sidebar'],
                             height=70)
        item_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=1)
        item_frame.grid_propagate(False)
        item_frame.columnconfigure(1, weight=1)

        # Avatar
        avatar_frame = tk.Frame(item_frame, bg=self.colors['sidebar'], width=50, height=50)
        avatar_frame.grid(row=0, column=0, padx=15, pady=10)
        avatar_frame.grid_propagate(False)

        initial = user['username'][0].upper()
        self.create_avatar(avatar_frame, initial)

        # User info
        info_frame = tk.Frame(item_frame, bg=self.colors['sidebar'])
        info_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=10)
        info_frame.columnconfigure(0, weight=1)

        # Username
        name_label = tk.Label(info_frame,
                             text=user['username'],
                             bg=self.colors['sidebar'],
                             fg=self.colors['text_primary'],
                             font=self.fonts['subtitle'],
                             anchor=tk.W)
        name_label.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # Status
        status_text = "Online" if user.get('is_online', False) else "Offline"
        status_color = self.colors['online'] if user.get('is_online', False) else self.colors['text_secondary']

        status_label = tk.Label(info_frame,
                               text=status_text,
                               bg=self.colors['sidebar'],
                               fg=status_color,
                               font=self.fonts['small'],
                               anchor=tk.W)
        status_label.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Click handler to start private chat
        def on_click(event=None):
            self.start_private_chat_with_user(user)

        # Bind click events
        for widget in [item_frame, avatar_frame, info_frame, name_label, status_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", lambda e: item_frame.config(bg='#F0F0F0'))
            widget.bind("<Leave>", lambda e: item_frame.config(bg=self.colors['sidebar']))

    def start_private_chat_with_user(self, user: Dict[str, Any]):
        """Start a private chat with a user."""
        def create_chat_thread():
            try:
                # Create or find existing private chat room
                room_name = f"Chat with {user['username']}"
                result = self.api_client.create_chat_room(room_name, "", False)

                if result:
                    # Join the room
                    self.api_client.join_chat_room(result['id'])

                    # Invite the other user (this would need backend support)
                    # For now, we'll just select the room
                    self.parent.after(0, lambda: self.select_room(result))
                    self.parent.after(0, lambda: self.show_status(f"Started chat with {user['username']}"))
                else:
                    self.parent.after(0, lambda: self.show_status("Failed to create chat room"))
            except Exception as e:
                self.parent.after(0, lambda: self.show_status(f"Error: {e}"))

        threading.Thread(target=create_chat_thread, daemon=True).start()

    def select_room(self, room: Dict[str, Any]):
        """Select and load a chat room."""
        self.current_room = room

        # Update chat header
        self.chat_name_label.config(text=room['name'])

        status_text = f"{room.get('member_count', 0)} members"
        if not room.get('is_group', False):
            status_text = "Private chat"

        self.chat_status_label.config(text=status_text)

        # Load messages
        self.load_messages(room['id'])

        # Join room via WebSocket
        if self.ws_client:
            self.ws_client.join_room(room['id'])

        self.show_status(f"Joined: {room['name']}")

    def load_messages(self, room_id: int):
        """Load messages for a room."""
        def load_messages_thread():
            try:
                messages = self.api_client.get_messages(room_id)
                self.parent.after(0, lambda: self.display_messages(room_id, messages))
            except Exception as e:
                self.parent.after(0, lambda: self.show_status(f"Error loading messages: {e}"))

        threading.Thread(target=load_messages_thread, daemon=True).start()

    def display_messages(self, room_id: int, messages: List[Dict[str, Any]]):
        """Display messages with modern chat bubbles."""
        if not self.current_room or self.current_room['id'] != room_id:
            return

        # Clear existing messages
        for widget in self.messages_scrollable_frame.winfo_children():
            widget.destroy()

        # Add messages
        for message in messages:
            self.add_message_bubble(message)

        # Scroll to bottom
        self.messages_canvas.update_idletasks()
        self.messages_canvas.yview_moveto(1.0)

    def add_message_bubble(self, message: Dict[str, Any]):
        """Add a modern message bubble with full functionality."""
        is_own = message.get('sender_id') == self.user_info['id']

        # Message container
        msg_container = tk.Frame(self.messages_scrollable_frame, bg=self.colors['chat_bg'])
        msg_container.grid(row=len(self.messages_scrollable_frame.winfo_children()),
                          column=0, sticky=(tk.W, tk.E), pady=2, padx=10)
        msg_container.columnconfigure(0, weight=1)

        # Store message ID for status updates
        msg_container.message_id = message.get('id')

        # Message bubble
        bubble_frame = tk.Frame(msg_container, bg=self.colors['chat_bg'])

        if is_own:
            bubble_frame.grid(row=0, column=0, sticky=tk.E, padx=(50, 0))
            bubble_bg = self.colors['sent_msg']
        else:
            bubble_frame.grid(row=0, column=0, sticky=tk.W, padx=(0, 50))
            bubble_bg = self.colors['received_msg']

        # Bubble content with rounded corners effect
        bubble = tk.Frame(bubble_frame, bg=bubble_bg, relief='solid', bd=1, padx=12, pady=8)
        bubble.pack()

        # Sender name (for group chats)
        if not is_own and self.current_room and self.current_room.get('is_group', False):
            sender_label = tk.Label(bubble,
                                   text=message.get('sender_username', 'Unknown'),
                                   bg=bubble_bg,
                                   fg=self.colors['accent'],
                                   font=self.fonts['small'])
            sender_label.pack(anchor=tk.W)

        # Message content based on type
        message_type = message.get('message_type', 'text')

        if message_type == 'text':
            content_label = tk.Label(bubble,
                                    text=message['content'],
                                    bg=bubble_bg,
                                    fg=self.colors['text_primary'],
                                    font=self.fonts['body'],
                                    wraplength=300,
                                    justify=tk.LEFT)
            content_label.pack(anchor=tk.W)

        elif message_type in ['image', 'document', 'audio']:
            # File message
            file_frame = tk.Frame(bubble, bg=bubble_bg)
            file_frame.pack(anchor=tk.W, fill=tk.X)

            # File icon
            file_icons = {'image': '🖼️', 'document': '📄', 'audio': '🎵'}
            icon_label = tk.Label(file_frame,
                                 text=file_icons.get(message_type, '📎'),
                                 bg=bubble_bg,
                                 font=self.fonts['emoji'])
            icon_label.pack(side=tk.LEFT, padx=(0, 5))

            # File name
            file_name = message.get('file_name', 'File')
            file_label = tk.Label(file_frame,
                                 text=file_name,
                                 bg=bubble_bg,
                                 fg=self.colors['text_primary'],
                                 font=self.fonts['body'])
            file_label.pack(side=tk.LEFT)

        # Bottom row with timestamp and status
        bottom_frame = tk.Frame(bubble, bg=bubble_bg)
        bottom_frame.pack(fill=tk.X, pady=(5, 0))

        # Timestamp
        try:
            timestamp = format_timestamp(message['created_at'])
        except:
            timestamp = datetime.now().strftime("%H:%M")

        time_label = tk.Label(bottom_frame,
                             text=timestamp,
                             bg=bubble_bg,
                             fg=self.colors['text_secondary'],
                             font=self.fonts['small'])
        time_label.pack(side=tk.RIGHT)

        # Message status (for own messages)
        if is_own:
            status = message.get('status', 'sent')
            status_text = self.get_status_icon(status)

            status_label = tk.Label(bottom_frame,
                                   text=status_text,
                                   bg=bubble_bg,
                                   fg=self.colors['text_secondary'],
                                   font=self.fonts['small'])
            status_label.pack(side=tk.RIGHT, padx=(0, 5))

            # Store reference for status updates
            msg_container.status_label = status_label

        # Add context menu for message actions
        self.add_message_context_menu(bubble, message)

        # Auto-scroll to bottom
        self.messages_canvas.update_idletasks()
        self.messages_canvas.yview_moveto(1.0)

    def get_status_icon(self, status: str) -> str:
        """Get status icon for message."""
        status_icons = {
            'sending': '🕐',
            'sent': '✓',
            'delivered': '✓✓',
            'read': '✓✓',
            'failed': '❌'
        }
        return status_icons.get(status, '✓')

    def add_message_context_menu(self, widget, message: Dict[str, Any]):
        """Add context menu to message bubble."""
        def show_context_menu(event):
            context_menu = tk.Menu(self.parent, tearoff=0)

            # Reply option
            context_menu.add_command(label="↩️ Reply",
                                   command=lambda: self.reply_to_message(message))

            # React option
            react_menu = tk.Menu(context_menu, tearoff=0)
            for emoji_char in ['👍', '❤️', '😂', '😮', '😢', '🙏']:
                react_menu.add_command(label=emoji_char,
                                     command=lambda e=emoji_char: self.add_reaction(message, e))
            context_menu.add_cascade(label="😊 React", menu=react_menu)

            # Forward option
            context_menu.add_command(label="➡️ Forward",
                                   command=lambda: self.forward_message(message))

            # Copy option (for text messages)
            if message.get('message_type') == 'text':
                context_menu.add_command(label="📋 Copy",
                                       command=lambda: self.copy_message(message))

            # Delete option (for own messages)
            if message.get('sender_id') == self.user_info['id']:
                context_menu.add_separator()
                context_menu.add_command(label="🗑️ Delete",
                                       command=lambda: self.delete_message(message))

            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        widget.bind("<Button-3>", show_context_menu)  # Right click

    def reply_to_message(self, message: Dict[str, Any]):
        """Reply to a message."""
        reply_text = f"Replying to: {message['content'][:50]}..."
        self.message_var.set(f"↩️ {reply_text}\n")
        self.message_entry.focus_set()

    def add_reaction(self, message: Dict[str, Any], emoji_char: str):
        """Add reaction to message."""
        messagebox.showinfo("Reaction", f"Added {emoji_char} reaction to message")

    def forward_message(self, message: Dict[str, Any]):
        """Forward message to another chat."""
        messagebox.showinfo("Forward", f"Forwarding message: {message['content'][:50]}...")

    def copy_message(self, message: Dict[str, Any]):
        """Copy message content to clipboard."""
        content = message.get('content', '')
        self.parent.clipboard_clear()
        self.parent.clipboard_append(content)
        messagebox.showinfo("Copied", "Message copied to clipboard")

    def delete_message(self, message: Dict[str, Any]):
        """Delete message."""
        if messagebox.askyesno("Delete Message", "Are you sure you want to delete this message?"):
            messagebox.showinfo("Deleted", "Message deleted")

    def send_message(self, event=None):
        """Send a message - FULLY FUNCTIONAL."""
        if not self.current_room:
            messagebox.showwarning("No Chat", "Please select a chat room first")
            return

        content = self.message_var.get().strip()
        if not content:
            return

        # Clear input immediately for better UX
        self.message_var.set("")

        # Add message to UI immediately (optimistic update)
        temp_message = {
            'id': f"temp_{int(time.time() * 1000)}",
            'content': content,
            'sender_id': self.user_info['id'],
            'sender_username': self.user_info['username'],
            'chat_room_id': self.current_room['id'],
            'created_at': datetime.now().isoformat(),
            'message_type': 'text',
            'status': 'sending'
        }

        self.add_message_bubble(temp_message)

        # Send message via API
        def send_message_thread():
            try:
                result = self.api_client.send_message(self.current_room['id'], content)
                if result:
                    # Update temp message with real data
                    self.parent.after(0, lambda: self.update_message_status(temp_message['id'], 'sent'))

                    # Send via WebSocket for real-time delivery
                    if self.ws_client:
                        self.ws_client.send_message(self.current_room['id'], content)
                else:
                    self.parent.after(0, lambda: self.update_message_status(temp_message['id'], 'failed'))
            except Exception as e:
                self.parent.after(0, lambda: self.update_message_status(temp_message['id'], 'failed'))
                print(f"Send message error: {e}")

        threading.Thread(target=send_message_thread, daemon=True).start()

    def setup_websocket(self):
        """Set up WebSocket connection."""
        token = self.token_manager.get_access_token()
        if token:
            self.ws_client = WebSocketClient(token, self.handle_websocket_message)
            self.ws_client.start()
            self.update_connection_status()

    def update_connection_status(self):
        """Update connection status display."""
        if self.ws_client:
            status = self.ws_client.connection_status
            if status == "Connected":
                self.connection_status_label.config(text="Connected", fg=self.colors['online'])
            elif "Connecting" in status:
                self.connection_status_label.config(text=status, fg=self.colors['typing'])
            else:
                self.connection_status_label.config(text=status, fg='red')

        # Schedule next update
        self.parent.after(2000, self.update_connection_status)

    def handle_websocket_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket message."""
        try:
            message_type = data.get('type')

            if message_type == 'message':
                self.handle_new_message(data.get('data', {}))
            elif message_type == 'typing_start':
                self.handle_typing_start(data)
            elif message_type == 'typing_stop':
                self.handle_typing_stop(data)
            elif message_type == 'user_online':
                self.handle_user_online(data)
            elif message_type == 'user_offline':
                self.handle_user_offline(data)
        except Exception as e:
            print(f"WebSocket message error: {e}")

    def handle_new_message(self, message_data: Dict[str, Any]):
        """Handle new message from WebSocket."""
        if self.current_room and message_data.get('chat_room_id') == self.current_room['id']:
            self.parent.after(0, lambda: self.add_message_bubble(message_data))

    def handle_typing_start(self, data: Dict[str, Any]):
        """Handle typing indicator."""
        if self.current_room and data.get('room_id') == self.current_room['id']:
            username = data.get('username', 'Someone')
            if username != self.user_info['username']:
                self.parent.after(0, lambda: self.typing_label.config(text=f"{username} is typing..."))

    def handle_typing_stop(self, data: Dict[str, Any]):
        """Handle typing stop."""
        if self.current_room and data.get('room_id') == self.current_room['id']:
            self.parent.after(0, lambda: self.typing_label.config(text=""))

    def handle_user_online(self, data: Dict[str, Any]):
        """Handle user coming online."""
        self.online_users.add(data.get('username'))
        self.refresh_chat_list()

    def handle_user_offline(self, data: Dict[str, Any]):
        """Handle user going offline."""
        self.online_users.discard(data.get('username'))
        self.refresh_chat_list()

    def on_typing(self, event=None):
        """Handle typing indicator."""
        if self.current_room and self.ws_client:
            self.ws_client.start_typing(self.current_room['id'])

    def update_message_status(self, temp_id: str, status: str):
        """Update message status in UI."""
        # Find and update the message bubble
        for widget in self.messages_scrollable_frame.winfo_children():
            if hasattr(widget, 'message_id') and widget.message_id == temp_id:
                # Update status indicator
                if hasattr(widget, 'status_label'):
                    if status == 'sent':
                        widget.status_label.config(text="✓", fg='gray')
                    elif status == 'delivered':
                        widget.status_label.config(text="✓✓", fg='gray')
                    elif status == 'read':
                        widget.status_label.config(text="✓✓", fg='blue')
                    elif status == 'failed':
                        widget.status_label.config(text="❌", fg='red')
                break

    def show_status(self, message: str):
        """Show status message in status bar."""
        if hasattr(self, 'connection_status_label'):
            self.connection_status_label.config(text=message)
        print(f"Status: {message}")

    def cleanup(self):
        """Clean up resources."""
        if self.ws_client:
            self.ws_client.stop()

    def destroy(self):
        """Destroy the window."""
        self.cleanup()
        for widget in self.parent.winfo_children():
            widget.destroy()
    
    def load_chat_rooms(self):
        """Load chat rooms."""
        # TODO: Implement chat room loading
        pass
    
    def setup_websocket(self):
        """Set up WebSocket connection."""
        # TODO: Implement WebSocket setup
        pass
    
    def cleanup(self):
        """Clean up resources."""
        if self.ws_client:
            self.ws_client.stop()
    
    def destroy(self):
        """Destroy the chat window."""
        self.cleanup()
        for widget in self.parent.winfo_children():
            widget.destroy()
