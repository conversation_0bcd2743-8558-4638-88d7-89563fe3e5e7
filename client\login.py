import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Callable, Optional
from helpers import APIClient, TokenManager

class LoginWindow:
    """Login and registration window."""
    
    def __init__(self, parent: ttk.Frame, on_login_success: Callable, api_client: APIClient, token_manager: TokenManager):
        self.parent = parent
        self.on_login_success = on_login_success
        self.api_client = api_client
        self.token_manager = token_manager
        
        # Set token manager for API client
        self.api_client.set_token_manager(self.token_manager)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the login UI."""
        # Clear parent frame
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        # Main container
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=20, pady=20)
        
        # Configure grid weights
        self.main_frame.columnconfigure(0, weight=1)
        
        # Title
        title_label = ttk.Label(self.main_frame, text="Real-Time Chat Application", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 30))
        
        # Notebook for tabs
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        
        # Login tab
        self.login_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(self.login_frame, text="Login")
        
        # Registration tab
        self.register_frame = ttk.Frame(self.notebook, padding="20")
        self.notebook.add(self.register_frame, text="Register")
        
        self.setup_login_tab()
        self.setup_register_tab()
        
        # Status label
        self.status_label = ttk.Label(self.main_frame, text="", foreground="red")
        self.status_label.grid(row=2, column=0, pady=(0, 10))
    
    def setup_login_tab(self):
        """Set up the login tab."""
        # Username
        ttk.Label(self.login_frame, text="Username:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.login_username_var = tk.StringVar()
        self.login_username_entry = ttk.Entry(self.login_frame, textvariable=self.login_username_var, width=30)
        self.login_username_entry.grid(row=1, column=0, pady=(0, 15))
        
        # Password
        ttk.Label(self.login_frame, text="Password:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.login_password_var = tk.StringVar()
        self.login_password_entry = ttk.Entry(self.login_frame, textvariable=self.login_password_var, 
                                            show="*", width=30)
        self.login_password_entry.grid(row=3, column=0, pady=(0, 20))
        
        # Login button
        self.login_button = ttk.Button(self.login_frame, text="Login", command=self.handle_login)
        self.login_button.grid(row=4, column=0, pady=(0, 10))
        
        # Bind Enter key to login
        self.login_username_entry.bind('<Return>', lambda e: self.handle_login())
        self.login_password_entry.bind('<Return>', lambda e: self.handle_login())
        
        # Focus on username entry
        self.login_username_entry.focus()
    
    def setup_register_tab(self):
        """Set up the registration tab."""
        # Username
        ttk.Label(self.register_frame, text="Username:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.register_username_var = tk.StringVar()
        self.register_username_entry = ttk.Entry(self.register_frame, textvariable=self.register_username_var, width=30)
        self.register_username_entry.grid(row=1, column=0, pady=(0, 15))
        
        # Email
        ttk.Label(self.register_frame, text="Email:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.register_email_var = tk.StringVar()
        self.register_email_entry = ttk.Entry(self.register_frame, textvariable=self.register_email_var, width=30)
        self.register_email_entry.grid(row=3, column=0, pady=(0, 15))
        
        # Full Name
        ttk.Label(self.register_frame, text="Full Name (optional):").grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        self.register_fullname_var = tk.StringVar()
        self.register_fullname_entry = ttk.Entry(self.register_frame, textvariable=self.register_fullname_var, width=30)
        self.register_fullname_entry.grid(row=5, column=0, pady=(0, 15))
        
        # Password
        ttk.Label(self.register_frame, text="Password:").grid(row=6, column=0, sticky=tk.W, pady=(0, 5))
        self.register_password_var = tk.StringVar()
        self.register_password_entry = ttk.Entry(self.register_frame, textvariable=self.register_password_var, 
                                               show="*", width=30)
        self.register_password_entry.grid(row=7, column=0, pady=(0, 15))
        
        # Confirm Password
        ttk.Label(self.register_frame, text="Confirm Password:").grid(row=8, column=0, sticky=tk.W, pady=(0, 5))
        self.register_confirm_password_var = tk.StringVar()
        self.register_confirm_password_entry = ttk.Entry(self.register_frame, 
                                                       textvariable=self.register_confirm_password_var, 
                                                       show="*", width=30)
        self.register_confirm_password_entry.grid(row=9, column=0, pady=(0, 20))
        
        # Register button
        self.register_button = ttk.Button(self.register_frame, text="Register", command=self.handle_register)
        self.register_button.grid(row=10, column=0, pady=(0, 10))
        
        # Bind Enter key to register
        self.register_confirm_password_entry.bind('<Return>', lambda e: self.handle_register())
    
    def handle_login(self):
        """Handle login button click."""
        username = self.login_username_var.get().strip()
        password = self.login_password_var.get()
        
        if not username or not password:
            self.show_status("Please enter both username and password", "red")
            return
        
        # Disable button and show loading
        self.login_button.config(state="disabled", text="Logging in...")
        self.show_status("Logging in...", "blue")
        
        # Perform login in background thread
        threading.Thread(target=self._login_thread, args=(username, password), daemon=True).start()
    
    def _login_thread(self, username: str, password: str):
        """Login in background thread."""
        try:
            result = self.api_client.login(username, password)
            
            if result:
                # Save tokens
                self.token_manager.save_token(
                    result["access_token"],
                    result["refresh_token"]
                )
                
                # Get user info
                user_info = self.api_client.get_current_user()
                
                if user_info:
                    # Call success callback on main thread
                    self.parent.after(0, lambda: self.on_login_success(user_info))
                else:
                    self.parent.after(0, lambda: self.show_login_error("Failed to get user information"))
            else:
                self.parent.after(0, lambda: self.show_login_error("Invalid username or password"))
        
        except Exception as e:
            self.parent.after(0, lambda: self.show_login_error(f"Login failed: {str(e)}"))
    
    def show_login_error(self, message: str):
        """Show login error and reset button."""
        self.login_button.config(state="normal", text="Login")
        self.show_status(message, "red")
    
    def handle_register(self):
        """Handle register button click."""
        username = self.register_username_var.get().strip()
        email = self.register_email_var.get().strip()
        full_name = self.register_fullname_var.get().strip()
        password = self.register_password_var.get()
        confirm_password = self.register_confirm_password_var.get()
        
        # Validation
        if not username or not email or not password:
            self.show_status("Please fill in all required fields", "red")
            return
        
        if password != confirm_password:
            self.show_status("Passwords do not match", "red")
            return
        
        if len(password) < 6:
            self.show_status("Password must be at least 6 characters", "red")
            return
        
        # Disable button and show loading
        self.register_button.config(state="disabled", text="Registering...")
        self.show_status("Creating account...", "blue")
        
        # Perform registration in background thread
        threading.Thread(target=self._register_thread, 
                        args=(username, email, password, full_name), daemon=True).start()
    
    def _register_thread(self, username: str, email: str, password: str, full_name: str):
        """Register in background thread."""
        try:
            result = self.api_client.register(username, email, password, full_name)
            
            if result:
                self.parent.after(0, lambda: self.show_register_success())
            else:
                self.parent.after(0, lambda: self.show_register_error("Registration failed. Username or email may already exist."))
        
        except Exception as e:
            self.parent.after(0, lambda: self.show_register_error(f"Registration failed: {str(e)}"))
    
    def show_register_success(self):
        """Show registration success."""
        self.register_button.config(state="normal", text="Register")
        self.show_status("Registration successful! Please login.", "green")
        
        # Switch to login tab and clear fields
        self.notebook.select(0)
        self.clear_register_fields()
    
    def show_register_error(self, message: str):
        """Show registration error and reset button."""
        self.register_button.config(state="normal", text="Register")
        self.show_status(message, "red")
    
    def clear_register_fields(self):
        """Clear registration form fields."""
        self.register_username_var.set("")
        self.register_email_var.set("")
        self.register_fullname_var.set("")
        self.register_password_var.set("")
        self.register_confirm_password_var.set("")
    
    def show_status(self, message: str, color: str = "black"):
        """Show status message."""
        self.status_label.config(text=message, foreground=color)
    
    def destroy(self):
        """Clean up the login window."""
        for widget in self.parent.winfo_children():
            widget.destroy()
