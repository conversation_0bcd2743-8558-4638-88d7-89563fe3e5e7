#!/usr/bin/env python3
"""
Simple startup script for the chat application.
Starts both backend and client with proper configuration.
"""

import os
import sys
import subprocess
import time
import threading
import requests
from pathlib import Path

def check_backend_health():
    """Check if backend is healthy."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=3)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """Start the backend server in a separate process."""
    backend_dir = Path(__file__).parent / "backend"
    
    print("🚀 Starting backend server...")
    
    if os.name == 'nt':  # Windows
        # Use PowerShell to start in new window
        cmd = [
            "powershell", "-Command",
            f"cd '{backend_dir}'; python -m uvicorn app.main:app --host 0.0.0.0 --port 8001"
        ]
        process = subprocess.Popen(cmd, creationflags=subprocess.CREATE_NEW_CONSOLE)
    else:  # Unix/Linux/Mac
        cmd = ["python3", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"]
        process = subprocess.Popen(cmd, cwd=backend_dir)
    
    # Wait for backend to be ready
    print("⏳ Waiting for backend to start...")
    for i in range(30):
        if check_backend_health():
            print("✅ Backend server is ready!")
            return process
        time.sleep(1)
        if i % 5 == 0:
            print(f"   Still waiting... ({i+1}/30)")
    
    print("❌ Backend failed to start within 30 seconds")
    return None

def start_client():
    """Start the client application."""
    client_dir = Path(__file__).parent / "client"
    
    print("🖥️ Starting client application...")
    
    if os.name == 'nt':  # Windows
        cmd = ["python", "main.py"]
    else:  # Unix/Linux/Mac
        cmd = ["python3", "main.py"]
    
    try:
        subprocess.run(cmd, cwd=client_dir)
    except KeyboardInterrupt:
        print("\n👋 Client application closed by user")
    except Exception as e:
        print(f"❌ Client error: {e}")

def main():
    """Main startup function."""
    print("💬 Chat Application Startup")
    print("=" * 40)
    
    # Check if backend is already running
    if check_backend_health():
        print("✅ Backend is already running")
        backend_process = None
    else:
        # Start backend
        backend_process = start_backend()
        if not backend_process:
            print("\n❌ Failed to start backend server")
            print("Please check:")
            print("1. Python dependencies are installed (pip install -r backend/requirements.txt)")
            print("2. No other service is using port 8001")
            print("3. Database is accessible")
            return 1
    
    print("\n" + "=" * 40)
    print("🎉 Backend is ready!")
    print("📱 Starting client application...")
    print("=" * 40)
    
    try:
        # Start client
        start_client()
    finally:
        # Clean up
        if backend_process:
            print("\n🛑 Stopping backend server...")
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend_process.kill()
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
